# 广告统计展示功能接口文档

## 概述

为广告管理系统新增了统计展示功能，包括基本信息统计、趋势图表和详细统计列表。

## 接口列表

### 1. 广告统计概览

**接口路径**: `POST /aapi/advertisement/stats/overview`

**功能**: 获取广告的基本统计信息，包括累计展示、累计点击、今日展示、今日点击等。

**请求参数**:
```json
{
  "position_code": "home",        // 可选，广告位置编码，为空则统计所有位置
  "advertisement_id": 1           // 可选，广告ID，为空则统计所有广告
}
```

**响应数据**:
```json
{
  "total_views": 23798,           // 累计展示
  "total_clicks": 9837,           // 累计点击
  "today_views": 3798,            // 今日展示
  "today_clicks": 1837,           // 今日点击
  "yesterday_views": 3452,        // 昨日展示
  "yesterday_clicks": 1491,       // 昨日点击
  "views_growth": 346,            // 展示增长量（今日-昨日）
  "clicks_growth": 346            // 点击增长量（今日-昨日）
}
```

### 2. 广告统计趋势图

**接口路径**: `POST /aapi/advertisement/stats/trend`

**功能**: 获取广告统计的趋势数据，支持累计数据和每日数据两种模式。

**请求参数**:
```json
{
  "position_code": "home",        // 可选，广告位置编码
  "advertisement_id": 1,          // 可选，广告ID
  "date_range": "7",              // 必填，时间范围: "7"-近7天, "30"-近30天, "custom"-自定义
  "start_date": "2024-01-01",     // 自定义时间范围时必填，开始日期(YYYY-MM-DD)
  "end_date": "2024-01-31",       // 自定义时间范围时必填，结束日期(YYYY-MM-DD)
  "data_type": "cumulative"       // 必填，数据类型: "cumulative"-累计数据, "daily"-每日数据
}
```

**响应数据**:
```json
{
  "labels": ["06/16", "06/17", "06/18", "06/19", "06/20", "06/21", "06/22"],
  "data": [
    {
      "date": "2024-06-16",
      "views": 2100,
      "clicks": 450
    },
    {
      "date": "2024-06-17",
      "views": 3200,
      "clicks": 680
    }
    // ... 更多数据
  ]
}
```

### 3. 广告详细统计列表

**接口路径**: `POST /aapi/advertisement/stats/list`

**功能**: 获取广告的详细统计列表，支持分页。

**请求参数**:
```json
{
  "position_code": "home",        // 可选，广告位置编码
  "advertisement_id": 1,          // 可选，广告ID
  "date_range": "7",              // 必填，时间范围: "7"-近7天, "30"-近30天, "custom"-自定义
  "start_date": "2024-01-01",     // 自定义时间范围时必填
  "end_date": "2024-01-31",       // 自定义时间范围时必填
  "current": 1,                   // 当前页码
  "page_size": 10                 // 每页数量
}
```

**响应数据**:
```json
{
  "current": 1,
  "page_size": 10,
  "total": 25,
  "list": [
    {
      "advertisement_id": 1,
      "position_code": "home",
      "position_name": "首页广告位",
      "title": "广告标题",
      "total_views": 12500,
      "total_clicks": 1250,
      "click_rate": "10.00%",
      "status": 1
    }
    // ... 更多数据
  ]
}
```

## 数据说明

### 统计数据来源

当前实现基于 `advertisement_banner_stats` 表，该表记录了广告banner的点击统计数据。

### 展示数据处理

由于当前数据表主要记录点击数据，展示数据暂时使用以下策略：
- 在概览接口中，展示数据暂时等同于点击数据
- 在趋势图中，展示数据暂时等同于点击数据  
- 在详细列表中，展示数据假设为点击数据的10倍

### 建议优化

为了更准确的统计，建议：
1. 在 `advertisement_banner_stats` 表中添加 `action_type` 字段区分展示和点击
2. 或者创建单独的展示统计表
3. 在前端展示广告时记录展示数据
4. 在用户点击广告时记录点击数据

## 使用示例

参考 `linux_amd64/advertisement.http` 文件中的测试用例，包含了所有接口的调用示例。

## 注意事项

1. 所有接口都需要认证，请在请求头中包含有效的 Bearer Token
2. 时间参数使用毫秒时间戳存储，但接口参数使用 YYYY-MM-DD 格式
3. 点击率计算公式：点击数 / 展示数 * 100%
4. 增长量计算：今日数据 - 昨日数据
