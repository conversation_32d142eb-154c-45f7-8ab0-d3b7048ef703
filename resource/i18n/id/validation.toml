"gf.gvalid.rule.required"                = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-if"             = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-unless"         = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-with"           = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-with-all"       = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-without"        = "Kolom {field} wajib diisi"
"gf.gvalid.rule.required-without-all"    = "Kolom {field} wajib diisi"

"gf.gvalid.rule.date"                    = "<PERSON><PERSON> {field} `{value}` bukan tanggal yang valid"
"gf.gvalid.rule.datetime"                = "Nilai {field} `{value}` bukan waktu yang valid"
"gf.gvalid.rule.date-format"             = "Nilai {field} `{value}` tidak sesuai format: {pattern}"

"gf.gvalid.rule.email"                   = "<PERSON><PERSON> {field} `{value}` bukan alamat email yang valid"
"gf.gvalid.rule.phone"                   = "<PERSON><PERSON> {field} `{value}` bukan nomor telepon seluler yang valid"
"gf.gvalid.rule.telephone"               = "Nilai {field} `{value}` bukan nomor telepon rumah yang valid"
"gf.gvalid.rule.passport"                = "Nilai {field} `{value}` bukan format paspor yang valid"
"gf.gvalid.rule.password"                = "Nilai {field} `{value}` bukan format kata sandi yang valid"
"gf.gvalid.rule.password2"               = "Nilai {field} `{value}` bukan format kata sandi yang valid"
"gf.gvalid.rule.password3"               = "Nilai {field} `{value}` bukan format kata sandi yang valid"
"gf.gvalid.rule.postcode"                = "Nilai {field} `{value}` bukan format kode pos yang valid"
"gf.gvalid.rule.resident-id"             = "Nilai {field} `{value}` bukan nomor identitas penduduk yang valid"
"gf.gvalid.rule.bank-card"               = "Nilai {field} `{value}` bukan nomor kartu bank yang valid"
"gf.gvalid.rule.qq"                      = "Nilai {field} `{value}` bukan nomor QQ yang valid"

"gf.gvalid.rule.ip"                      = "Nilai {field} `{value}` bukan alamat IP yang valid"
"gf.gvalid.rule.ipv4"                    = "Nilai {field} `{value}` bukan alamat IPv4 yang valid"
"gf.gvalid.rule.ipv6"                    = "Nilai {field} `{value}` bukan alamat IPv6 yang valid"
"gf.gvalid.rule.mac"                     = "Nilai {field} `{value}` bukan alamat MAC yang valid"
"gf.gvalid.rule.url"                     = "Nilai {field} `{value}` bukan URL yang valid"
"gf.gvalid.rule.domain"                  = "Nilai {field} `{value}` bukan format domain yang valid"

"gf.gvalid.rule.length"                  = "Panjang nilai {field} `{value}` harus antara {min} sampai {max}"
"gf.gvalid.rule.min-length"              = "Panjang nilai {field} `{value}` minimal {min}"
"gf.gvalid.rule.max-length"              = "Panjang nilai {field} `{value}` maksimal {max}"
"gf.gvalid.rule.size"                    = "Panjang nilai {field} `{value}` harus tepat {size}"

"gf.gvalid.rule.between"                 = "Nilai {field} `{value}` harus antara {min} dan {max}"
"gf.gvalid.rule.min"                     = "Nilai {field} `{value}` minimal {min}"
"gf.gvalid.rule.max"                     = "Nilai {field} `{value}` maksimal {max}"

"gf.gvalid.rule.json"                    = "Nilai {field} `{value}` bukan string JSON yang valid"
"gf.gvalid.rule.xml"                     = "Nilai {field} `{value}` bukan string XML yang valid"
"gf.gvalid.rule.array"                   = "Nilai {field} `{value}` bukan array"

"gf.gvalid.rule.integer"                 = "Nilai {field} `{value}` bukan bilangan bulat"
"gf.gvalid.rule.boolean"                 = "Kolom {field} harus bernilai true atau false"

"gf.gvalid.rule.same"                    = "Nilai {field} `{value}` harus sama dengan kolom {pattern}"
"gf.gvalid.rule.different"               = "Nilai {field} `{value}` harus berbeda dari kolom {pattern}"

"gf.gvalid.rule.in"                      = "Nilai {field} `{value}` tidak ada dalam rentang yang diizinkan: {pattern}"
"gf.gvalid.rule.not-in"                  = "Nilai {field} `{value}` tidak boleh ada dalam rentang: {pattern}"

"gf.gvalid.rule.regex"                   = "Nilai {field} `{value}` harus sesuai pola regex: {pattern}"

"gf.gvalid.rule.gf.gvalid.rule.__default__" = "Nilai :attribute `:value` tidak valid"