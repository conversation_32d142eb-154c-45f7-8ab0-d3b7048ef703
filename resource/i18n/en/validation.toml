"gf.gvalid.rule.required" =              "The {field} field is required"
"gf.gvalid.rule.required-if" =           "The {field} field is required"
"gf.gvalid.rule.required-unless" =       "The {field} field is required"
"gf.gvalid.rule.required-with" =         "The {field} field is required"
"gf.gvalid.rule.required-with-all" =     "The {field} field is required"
"gf.gvalid.rule.required-without" =      "The {field} field is required"
"gf.gvalid.rule.required-without-all" =  "The {field} field is required"
"gf.gvalid.rule.date" =                  "The {field} value `{value}` is not a valid date"
"gf.gvalid.rule.datetime" =              "The {field} value `{value}` is not a valid datetime"
"gf.gvalid.rule.date-format" =           "The {field} value `{value}` does not match the format: {pattern}"
"gf.gvalid.rule.email" =                 "The {field} value `{value}` is not a valid email address"
"gf.gvalid.rule.phone" =                 "The {field} value `{value}` is not a valid phone number"
"gf.gvalid.rule.telephone" =             "The {field} value `{value}` is not a valid telephone number"
"gf.gvalid.rule.passport" =              "The {field} value `{value}` is not a valid passport format"
"gf.gvalid.rule.password" =              "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.password2" =             "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.password3" =             "The {field} value `{value}` is not a valid password format"
"gf.gvalid.rule.postcode" =              "The {field} value `{value}` is not a valid postcode format"
"gf.gvalid.rule.resident-id" =           "The {field} value `{value}` is not a valid resident id number"
"gf.gvalid.rule.bank-card" =             "The {field} value `{value}` is not a valid bank card number"
"gf.gvalid.rule.qq" =                    "The {field} value `{value}` is not a valid QQ number"
"gf.gvalid.rule.ip" =                    "The {field} value `{value}` is not a valid IP address"
"gf.gvalid.rule.ipv4" =                  "The {field} value `{value}` is not a valid IPv4 address"
"gf.gvalid.rule.ipv6" =                  "The {field} value `{value}` is not a valid IPv6 address"
"gf.gvalid.rule.mac" =                   "The {field} value `{value}` is not a valid MAC address"
"gf.gvalid.rule.url" =                   "The {field} value `{value}` is not a valid URL address"
"gf.gvalid.rule.domain" =                "The {field} value `{value}` is not a valid domain format"
"gf.gvalid.rule.length" =                "The {field} value `{value}` length must be between {min} and {max}"
"gf.gvalid.rule.min-length" =            "The {field} value `{value}` length must be equal or greater than {min}"
"gf.gvalid.rule.max-length" =            "The {field} value `{value}` length must be equal or lesser than {max}"
"gf.gvalid.rule.size" =                  "The {field} value `{value}` length must be {size}"
"gf.gvalid.rule.between" =               "The {field} value `{value}` must be between {min} and {max}"
"gf.gvalid.rule.min" =                   "The {field} value `{value}` must be equal or greater than {min}"
"gf.gvalid.rule.max" =                   "The {field} value `{value}` must be equal or lesser than {max}"
"gf.gvalid.rule.json" =                  "The {field} value `{value}` is not a valid JSON string"
"gf.gvalid.rule.xml" =                   "The {field} value `{value}` is not a valid XML string"
"gf.gvalid.rule.array" =                 "The {field} value `{value}` is not an array"
"gf.gvalid.rule.integer" =               "The {field} value `{value}` is not an integer"
"gf.gvalid.rule.boolean" =               "The {field} value `{value}` field must be true or false"
"gf.gvalid.rule.same" =                  "The {field} value `{value}` must be the same as field {pattern}"
"gf.gvalid.rule.different" =             "The {field} value `{value}` must be different from field {pattern}"
"gf.gvalid.rule.in" =                    "The {field} value `{value}` is not in acceptable range: {pattern}"
"gf.gvalid.rule.not-in" =                "The {field} value `{value}` must not be in range: {pattern}"
"gf.gvalid.rule.regex" =                 "The {field} value `{value}` must be in regex of: {pattern}"
"gf.gvalid.rule.gf.gvalid.rule.__default__" = "The :attribute value `:value` is invalid"