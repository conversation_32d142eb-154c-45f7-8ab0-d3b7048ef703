package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ==================== 地标类型相关接口 ====================

// UmrahLandmarkTypeListReq 地标类型列表请求
type UmrahLandmarkTypeListReq struct {
	g.Meta `path:"/umrahLandmarkType/list" method:"post" tags:"副朝地标管理" summary:"地标类型列表"`
	ListReq
}

type UmrahLandmarkTypeListItem struct {
	Id          int               `json:"id" dc:"类型ID"`
	IconType    string            `json:"icon_type" dc:"图标类型"`
	TypeName    string            `json:"type_name" dc:"类型名称"`
	UseCount    int               `json:"use_count" dc:"使用次数"`
	LanguageArr []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
}

type UmrahLandmarkTypeListRes struct {
	List []UmrahLandmarkTypeListItem `json:"list" dc:"类型列表"`
	ListRes
}

// UmrahLandmarkTypeCreateReq 新增地标类型请求
type UmrahLandmarkTypeCreateReq struct {
	g.Meta   `path:"/umrahLandmarkType/add" method:"post" tags:"副朝地标管理" summary:"新增地标类型"`
	IconType string                        `v:"required" json:"icon_type" dc:"图标类型"`
	Content  []UmrahLandmarkTypeCreateItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type UmrahLandmarkTypeCreateItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type UmrahLandmarkTypeCreateRes struct {
	Id int `json:"id" dc:"类型ID"`
}

// UmrahLandmarkTypeEditReq 编辑地标类型请求
type UmrahLandmarkTypeEditReq struct {
	g.Meta   `path:"/umrahLandmarkType/edit" method:"post" tags:"副朝地标管理" summary:"编辑地标类型"`
	Id       int                         `v:"required" json:"id" dc:"类型ID"`
	IconType string                      `v:"required" json:"icon_type" dc:"图标类型"`
	Content  []UmrahLandmarkTypeEditItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type UmrahLandmarkTypeEditItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type UmrahLandmarkTypeEditRes struct{}

// UmrahLandmarkTypeOneReq 获取单个地标类型请求
type UmrahLandmarkTypeOneReq struct {
	g.Meta `path:"/umrahLandmarkType/one" method:"post" tags:"副朝地标管理" summary:"获取单个地标类型"`
	Id     int `v:"required" json:"id" dc:"类型ID"`
}

type UmrahLandmarkTypeOneRes struct {
	Id       int                         `v:"required" json:"id" dc:"类型ID"`
	IconType string                      `v:"required" json:"icon_type" dc:"图标类型"`
	Content  []UmrahLandmarkTypeEditItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

// UmrahLandmarkTypeDeleteReq 删除地标类型请求
type UmrahLandmarkTypeDeleteReq struct {
	g.Meta `path:"/umrahLandmarkType/delete" method:"post" tags:"副朝地标管理" summary:"删除地标类型"`
	Id     int `v:"required" json:"id" dc:"类型ID"`
}

type UmrahLandmarkTypeDeleteRes struct{}

// ==================== 地标相关接口 ====================

// UmrahLandmarkListReq 地标列表请求
type UmrahLandmarkListReq struct {
	g.Meta    `path:"/umrahLandmark/list" method:"post" tags:"副朝地标管理" summary:"地标列表"`
	InnerType string `json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	ListReq
}

type UmrahLandmarkListItem struct {
	Id               int               `json:"id" dc:"地标ID"`
	TypeId           int               `json:"type_id" dc:"地标类型ID"`
	TypeName         string            `json:"type_name" dc:"地标类型名称"`
	LandmarkName     string            `json:"landmark_name" dc:"地标名称"`
	ShortDescription string            `json:"short_description" dc:"地标简介"`
	Country          string            `json:"country" dc:"国家/地区"`
	Address          string            `json:"address" dc:"地址"`
	Latitude         string            `json:"latitude" dc:"纬度"`
	Longitude        string            `json:"longitude" dc:"经度"`
	ImageUrl         string            `json:"image_url" dc:"图片URL"`
	LanguageArr      []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
}

type UmrahLandmarkListRes struct {
	List []UmrahLandmarkListItem `json:"list" dc:"地标列表"`
	ListRes
}

// UmrahLandmarkCreateReq 新增地标请求
type UmrahLandmarkCreateReq struct {
	g.Meta    `path:"/umrahLandmark/add" method:"post" tags:"副朝地标管理" summary:"新增地标"`
	InnerType string                    `v:"required|in:destinasi,tokoh" json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	TypeId    int                       `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string                    `v:"required" json:"latitude" dc:"纬度"`
	Longitude string                    `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string                    `v:"required" json:"image_url" dc:"图片URL"`
	Content   []UmrahLandmarkCreateItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type UmrahLandmarkCreateItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type UmrahLandmarkCreateRes struct {
	Id int `json:"id" dc:"地标ID"`
}

// UmrahLandmarkEditReq 编辑地标请求
type UmrahLandmarkEditReq struct {
	g.Meta    `path:"/umrahLandmark/edit" method:"post" tags:"副朝地标管理" summary:"编辑地标"`
	Id        int                     `v:"required" json:"id" dc:"地标ID"`
	TypeId    int                     `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string                  `v:"required" json:"latitude" dc:"纬度"`
	Longitude string                  `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string                  `v:"required" json:"image_url" dc:"图片URL"`
	Content   []UmrahLandmarkEditItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type UmrahLandmarkEditItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type UmrahLandmarkEditRes struct{}

// UmrahLandmarkOneReq 获取单个地标请求
type UmrahLandmarkOneReq struct {
	g.Meta `path:"/umrahLandmark/one" method:"post" tags:"副朝地标管理" summary:"获取单个地标"`
	Id     int `v:"required" json:"id" dc:"地标ID"`
}

type UmrahLandmarkOneRes struct {
	UmrahLandmarkDetail
}

type UmrahLandmarkDetail struct {
	Id        int                 `v:"required" json:"id" dc:"地标ID"`
	InnerType string              `v:"required|in:destinasi,tokoh" json:"inner_type" dc:"内部类型: (destinasi, tokoh)"`
	TypeId    int                 `v:"required" json:"type_id" dc:"类型ID"`
	Latitude  string              `v:"required" json:"latitude" dc:"纬度"`
	Longitude string              `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string              `v:"required" json:"image_url" dc:"图片URL"`
	Content   []UmrahLandmarkItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type UmrahLandmarkItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	Country          string `v:"required|length:1,100" json:"country" dc:"国家/地区"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

// UmrahLandmarkDeleteReq 删除地标请求
type UmrahLandmarkDeleteReq struct {
	g.Meta `path:"/umrahLandmark/delete" method:"post" tags:"副朝地标管理" summary:"删除地标"`
	Id     int `v:"required" json:"id" dc:"地标ID"`
}

type UmrahLandmarkDeleteRes struct{}
