package v1

import "github.com/gogf/gf/v2/frame/g"

type UserProtocolSetReq struct {
	g.Meta      `path:"/systemSetting/userProtocol/set" method:"post" tags:"系统设置" summary:"用户协议设置"`
	ProtocolArr []ProtocolArrItem `v:"required" json:"protocol_arr" dc:"协议内容数组"`
}

type UserProtocolInfoReq struct {
	g.Meta `path:"/systemSetting/userProtocol/info" method:"post,get" tags:"系统设置" summary:"用户协议详情"`
}

type UserProtocolInfoRes struct {
	ProtocolArr []ProtocolArrItem `json:"protocol_arr" dc:"协议内容数组"`
}

type ProtocolArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	Content      string `v:"required" json:"content" dc:"内容"`
}

type PrivacyPolicySetReq struct {
	g.Meta           `path:"/systemSetting/privacyPolicy/set" method:"post" tags:"系统设置" summary:"隐私政策"`
	PrivacyPolicyArr []PrivacyPolicyItem `v:"required" json:"privacy_policy_arr" dc:"隐私政策内容数组"`
}

type PrivacyPolicyInfoReq struct {
	g.Meta `path:"/systemSetting/privacyPolicy/info" method:"post,get" tags:"系统设置" summary:"用户协议详情"`
}

type PrivacyPolicyInfoRes struct {
	PrivacyPolicyArr []PrivacyPolicyItem `json:"protocol_arr" dc:"隐私政策内容数组"`
}

type PrivacyPolicyItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	Content      string `v:"required" json:"content" dc:"内容"`
}

type SafetySettingSetReq struct {
	g.Meta `path:"/systemSetting/safetySetting/set" method:"post" tags:"系统设置" summary:"安全设置"`
	SafetySetting
}

type SafetySettingInfoReq struct {
	g.Meta `path:"/systemSetting/safetySetting/info" method:"post,get" tags:"系统设置" summary:"安全设置详情"`
}

type SafetySettingInfoRes struct {
	SafetySetting
}

type SafetySetting struct {
	MobileInterval        int `json:"mobile_interval" dc:"同一手机号发送间隔(秒)"`
	MobileIntervalMaxSend int `json:"mobile_interval_max_send" dc:"同一手机号间隔内最多发送次数"`
	MobileDailyLimit      int `json:"mobile_daily_limit" dc:"单手机号每日限额"`
	IpHourlyMobileLimit   int `json:"ip_hourly_mobile_limit" dc:"单IP每小时手机号数量限额"`
	IpDailySendLimit      int `json:"ip_daily_send_limit" dc:"单IP每日发送数量限额"`
	MobileIpMinute        int `json:"mobile_ip_minute" dc:"单手机号IP时间范围（分钟）"`
	MobileIpMaxRetry      int `json:"mobile_ip_max_retry" dc:"单手机号IP最大重试次数"`
}

type SensitiveWordFilteringSetReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordFiltering/set" method:"post" tags:"系统设置" summary:"敏感词过滤"`
	IsOpen int `v:"required|between:0,1" d:"0" json:"is_open" dc:"是否开启 0：关闭 1：开启"`
}

type SensitiveWordFilteringInfoReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordFiltering/info" method:"post,get" tags:"系统设置" summary:"敏感词过滤详情"`
}

type SensitiveWordFilteringInfoRes struct {
	IsOpen int `json:"is_open" dc:"是否开启 0：关闭 1：开启"`
}

type SensitiveWordCategoryAddReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordCategory/add" method:"post" tags:"系统设置" summary:"敏感词类型添加"`
	Word   string `v:"required" json:"word" dc:"敏感词类型"`
	Sort   int    `v:"required|between:0,9999" json:"sort" dc:"排序"`
}

type SensitiveWordCategoryListReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordCategory/list" method:"post,get" tags:"系统设置" summary:"敏感词类型列表"`
	Word   string `json:"word" dc:"敏感词类型"`
	ListReq
}

type SensitiveWordCategoryListRes struct {
	List []SensitiveWordCategoryInfoRes `json:"list"`
	ListRes
}

type SensitiveWordCategoryEditReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordCategory/edit" method:"post" tags:"系统设置" summary:"敏感词类型编辑"`
	Id     int    `v:"required" json:"id" dc:"敏感词类型ID"`
	Word   string `v:"required" json:"word" dc:"敏感词类型"`
	Sort   int    `v:"required|between:0,9999" json:"sort" dc:"排序"`
}

type SensitiveWordCategoryDelReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordCategory/del" method:"post" tags:"系统设置" summary:"敏感词类型删除"`
	Ids    []int `v:"required" json:"ids" dc:"敏感词类型id数组"`
}

type SensitiveWordCategoryInfoReq struct {
	g.Meta `path:"/systemSetting/sensitiveWordCategory/info" method:"post,get" tags:"系统设置" summary:"敏感词类型详情"`
	Id     int `v:"required" json:"id" dc:"敏感词类型ID"`
}

type SensitiveWordCategoryInfoRes struct {
	Id   int    `json:"id" dc:"敏感词类型ID"`
	Word string `json:"word" dc:"敏感词类型"`
	Sort int    `json:"sort" dc:"排序"`
}

type SensitiveWordAddReq struct {
	g.Meta     `path:"/systemSetting/sensitiveWord/add" method:"post" tags:"系统设置" summary:"敏感词类型添加"`
	Word       string `v:"required" json:"word" dc:"敏感词"`
	CategoryId int    `v:"required" json:"categoryId" dc:"敏感词类型ID"`
	MatchType  int    `v:"required|between:0,2" json:"match_type" dc:"匹配规则 0：全匹配 1：模糊匹配 2：正则匹配"`
	Status     int    `v:"required|between:0,1" d:"1" json:"status" dc:"状态 0：停用 1：启用 默认 1 启用"`
}

type SensitiveWordListReq struct {
	g.Meta     `path:"/systemSetting/sensitiveWord/list" method:"post,get" tags:"系统设置" summary:"敏感词列表"`
	Word       string `json:"word" dc:"敏感词"`
	Status     int    `json:"status" d:"-1" dc:"状态 0：停用 1：启用 默认 -1 全部"`
	CategoryId int    `json:"category_id" dc:"敏感词类型ID"`
	ListReq
}

type SensitiveWordListRes struct {
	List []SensitiveWordListItem `json:"list"`
	ListRes
}

type SensitiveWordListItem struct {
	Id            int    `json:"id" dc:"敏感词类型ID"`
	SerialNumber  int    `json:"serial_number" dc:"序号"`
	Word          string `json:"word" dc:"敏感词"`
	Status        int    `json:"status" dc:"状态 0：停用 1：启用"`
	StatusText    string `json:"status_text" dc:"状态文案"`
	CategoryId    int    `json:"category_id" dc:"敏感词类型ID"`
	CategoryName  string `json:"category_name" dc:"敏感词类型名称"`
	MatchType     int    `json:"match_type" dc:"匹配规则 0：全匹配 1：模糊匹配 2：正则匹配"`
	MatchTypeText string `json:"match_type_text" dc:"匹配规则文案"`
	CreateTime    string `json:"create_time" dc:"创建时间"`
}

type SensitiveWordEditReq struct {
	g.Meta     `path:"/systemSetting/sensitiveWord/edit" method:"post" tags:"系统设置" summary:"敏感词编辑"`
	Id         int    `v:"required" json:"id" dc:"敏感词ID"`
	Word       string `v:"required" json:"word" dc:"敏感词"`
	CategoryId int    `v:"required" json:"categoryId" dc:"敏感词类型ID"`
	MatchType  int    `v:"required|between:0,2" json:"match_type" dc:"匹配规则 0：全匹配 1：模糊匹配 2：正则匹配"`
	Status     int    `v:"required|between:0,1" d:"1" json:"status" dc:"状态 0：停用 1：启用 默认 1 启用"`
}

type SensitiveWordDelReq struct {
	g.Meta `path:"/systemSetting/sensitiveWord/del" method:"post" tags:"系统设置" summary:"敏感词删除"`
	Ids    []int `v:"required" json:"ids" dc:"敏感词id数组"`
}

type SensitiveWordInfoReq struct {
	g.Meta `path:"/systemSetting/sensitiveWord/info" method:"post,get" tags:"系统设置" summary:"敏感词详情"`
	Id     int `v:"required" json:"id" dc:"敏感词类型ID"`
}

type SensitiveWordInfoRes struct {
	Id         int    `v:"required" json:"id" dc:"敏感词ID"`
	Word       string `v:"required" json:"word" dc:"敏感词"`
	CategoryId int    `v:"required" json:"categoryId" dc:"敏感词类型ID"`
	MatchType  int    `v:"required|between:0,2" json:"match_type" dc:"匹配规则 0：全匹配 1：模糊匹配 2：正则匹配"`
	Status     int    `v:"required|between:0,1" d:"1" json:"status" dc:"状态 0：停用 1：启用 默认 1 启用"`
}

type SensitiveWordMatchTypeListReq struct {
	g.Meta `path:"/systemSetting/sensitiveWord/matchTypeList" method:"post,get" tags:"系统设置" summary:"敏感词匹配规则"`
}

type SensitiveWordMatchTypeListRes struct {
	List []SensitiveWordMatchTypeListItem `json:"list" dc:"列表"`
}

type SensitiveWordMatchTypeListItem struct {
	Id   int    `json:"id" dc:"键"`
	Name string `json:"name" dc:"值"`
}
