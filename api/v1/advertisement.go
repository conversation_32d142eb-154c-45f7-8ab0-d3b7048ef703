package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 广告位置列表
type AdvertisementPositionListReq struct {
	g.Meta `path:"/advertisement/position/list" tags:"广告管理" method:"post" summary:"广告位置列表"`
}

type AdvertisementPositionListRes struct {
	List []AdvertisementPositionItem `json:"list" dc:"广告位置列表"`
}

type AdvertisementPositionItem struct {
	Id           int    `json:"id" dc:"主键ID"`
	PositionName string `json:"position_name" dc:"广告位名称"`
	PositionCode string `json:"position_code" dc:"位置编码。目前只支持home/ibadah"`
	Remark       string `json:"remark" dc:"备注"`
}

// 修改广告位置备注
type AdvertisementPositionEditReq struct {
	g.Meta `path:"/advertisement/position/edit" tags:"广告管理" method:"post" summary:"修改广告位置备注"`
	Id     int    `json:"id" v:"required|min:1#Please select advertisement position|Invalid advertisement position ID" dc:"广告位置ID"`
	Remark string `json:"remark" v:"max-length:500#Remark length cannot exceed 500 characters" dc:"备注"`
}

type AdvertisementPositionEditRes struct {
	EmptyDataRes
}

// 广告列表
type AdvertisementListReq struct {
	g.Meta       `path:"/advertisement/list" tags:"广告管理" method:"post" summary:"广告列表"`
	Name         string `json:"name" dc:"广告名称"`
	PositionCode string `json:"position_code" dc:"广告位置编码"`
	Status       *int   `json:"status" dc:"状态: 0-禁用, 1-启用"`
	ListReq
}

type AdvertisementListRes struct {
	ListRes
	List []AdvertisementListItem `json:"list" dc:"广告列表"`
}

type AdvertisementListItem struct {
	AdvertisementId int               `json:"advertisement_id" dc:"广告ID"`
	PositionCode    string            `json:"position_code" dc:"广告位置编码"`
	PositionName    string            `json:"position_name" dc:"广告位置名称"`
	Title           string            `json:"title" dc:"广告名称"`
	Status          int               `json:"status" dc:"状态: 0-禁用, 1-启用"`
	LanguageArr     []LanguageArrItem `json:"language_arr" dc:"多语言支持信息"`
	CreateTime      int64             `json:"create_time" dc:"创建时间"`
}

// 广告详情
type AdvertisementOneReq struct {
	g.Meta          `path:"/advertisement/one" tags:"广告管理" method:"post" summary:"广告详情"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
}

type AdvertisementOneRes struct {
	Advertisement AdvertisementDetail `json:"advertisement" dc:"广告详情"`
}

type AdvertisementDetail struct {
	AdvertisementId int                           `json:"advertisement_id" dc:"广告ID"`
	PositionCode    string                        `json:"position_code" dc:"广告位置编码"`
	Status          int                           `json:"status" dc:"状态: 0-禁用, 1-启用"`
	Languages       []AdvertisementLanguageDetail `json:"languages" dc:"多语言内容"`
}

type AdvertisementLanguageDetail struct {
	LanguageType     int            `json:"language_type" dc:"语言类型"`
	Title            string         `json:"title" dc:"广告名称"`
	DisplayType      int            `json:"display_type" dc:"显示类型: 1-单图固定, 2-多图轮播"`
	CarouselInterval int            `json:"carousel_interval" dc:"轮播间隔时间(秒)"`
	Banners          []BannerDetail `json:"banners" dc:"Banner图片"`
}

type BannerDetail struct {
	BannerId int    `json:"banner_id" dc:"Banner ID"`
	ImageUrl string `json:"image_url" dc:"图片URL"`
	LinkUrl  string `json:"link_url" dc:"跳转链接"`
}

// 新增广告
type AdvertisementAddReq struct {
	g.Meta       `path:"/advertisement/add" tags:"广告管理" method:"post" summary:"新增广告"`
	PositionCode string                        `json:"position_code" v:"required#Please select advertisement position" dc:"广告位置编码"`
	Languages    []AdvertisementLanguageDetail `json:"languages" v:"required#Please set multi-language content" dc:"多语言内容"`
}

type AdvertisementAddRes struct {
	AdvertisementId int `json:"advertisement_id" dc:"新增广告ID"`
}

// 编辑广告
type AdvertisementEditReq struct {
	g.Meta          `path:"/advertisement/edit" tags:"广告管理" method:"post" summary:"编辑广告"`
	AdvertisementId int                           `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
	PositionCode    string                        `json:"position_code" v:"required#Please select advertisement position" dc:"广告位置编码"`
	Languages       []AdvertisementLanguageDetail `json:"languages" v:"required#Please set multi-language content" dc:"多语言内容"`
}

type AdvertisementEditRes struct {
	EmptyDataRes
}

// 删除广告
type AdvertisementDeleteReq struct {
	g.Meta          `path:"/advertisement/delete" tags:"广告管理" method:"post" summary:"删除广告"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
}

type AdvertisementDeleteRes struct {
	EmptyDataRes
}

// 切换广告状态
type AdvertisementToggleStatusReq struct {
	g.Meta          `path:"/advertisement/toggle-status" tags:"广告管理" method:"post" summary:"切换广告状态"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
	Status          int `json:"status" v:"required|in:0,1#Please select status|Invalid status value" dc:"状态: 0-下线, 1-上线"`
}

type AdvertisementToggleStatusRes struct {
	EmptyDataRes
}
