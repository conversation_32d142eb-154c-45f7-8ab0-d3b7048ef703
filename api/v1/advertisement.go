package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 广告位置列表
type AdvertisementPositionListReq struct {
	g.Meta `path:"/advertisement/position/list" tags:"广告管理" method:"post" summary:"广告位置列表"`
}

type AdvertisementPositionListRes struct {
	List []AdvertisementPositionItem `json:"list" dc:"广告位置列表"`
}

type AdvertisementPositionItem struct {
	Id           int    `json:"id" dc:"主键ID"`
	PositionName string `json:"position_name" dc:"广告位名称"`
	PositionCode string `json:"position_code" dc:"位置编码。目前只支持home/ibadah"`
	Remark       string `json:"remark" dc:"备注"`
}

// 修改广告位置备注
type AdvertisementPositionEditReq struct {
	g.Meta `path:"/advertisement/position/edit" tags:"广告管理" method:"post" summary:"修改广告位置备注"`
	Id     int    `json:"id" v:"required|min:1#Please select advertisement position|Invalid advertisement position ID" dc:"广告位置ID"`
	Remark string `json:"remark" v:"max-length:500#Remark length cannot exceed 500 characters" dc:"备注"`
}

type AdvertisementPositionEditRes struct {
	EmptyDataRes
}

// 广告列表
type AdvertisementListReq struct {
	g.Meta       `path:"/advertisement/list" tags:"广告管理" method:"post" summary:"广告列表"`
	Name         string `json:"name" dc:"广告名称"`
	PositionCode string `json:"position_code" dc:"广告位置编码"`
	Status       *int   `json:"status" dc:"状态: 0-禁用, 1-启用"`
	ListReq
}

type AdvertisementListRes struct {
	ListRes
	List []AdvertisementListItem `json:"list" dc:"广告列表"`
}

type AdvertisementListItem struct {
	AdvertisementId int               `json:"advertisement_id" dc:"广告ID"`
	PositionCode    string            `json:"position_code" dc:"广告位置编码"`
	PositionName    string            `json:"position_name" dc:"广告位置名称"`
	Title           string            `json:"title" dc:"广告名称"`
	Status          int               `json:"status" dc:"状态: 0-禁用, 1-启用"`
	LanguageArr     []LanguageArrItem `json:"language_arr" dc:"多语言支持信息"`
	CreateTime      int64             `json:"create_time" dc:"创建时间"`
}

// 广告详情
type AdvertisementOneReq struct {
	g.Meta          `path:"/advertisement/one" tags:"广告管理" method:"post" summary:"广告详情"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
}

type AdvertisementOneRes struct {
	Advertisement AdvertisementDetail `json:"advertisement" dc:"广告详情"`
}

type AdvertisementDetail struct {
	AdvertisementId int                           `json:"advertisement_id" dc:"广告ID"`
	PositionCode    string                        `json:"position_code" dc:"广告位置编码"`
	Status          int                           `json:"status" dc:"状态: 0-禁用, 1-启用"`
	Languages       []AdvertisementLanguageDetail `json:"languages" dc:"多语言内容"`
}

type AdvertisementLanguageDetail struct {
	LanguageType     int            `json:"language_type" dc:"语言类型"`
	Title            string         `json:"title" dc:"广告名称"`
	DisplayType      int            `json:"display_type" dc:"显示类型: 1-单图固定, 2-多图轮播"`
	CarouselInterval int            `json:"carousel_interval" dc:"轮播间隔时间(秒)"`
	Banners          []BannerDetail `json:"banners" dc:"Banner图片"`
}

type BannerDetail struct {
	BannerId int    `json:"banner_id" dc:"Banner ID"`
	ImageUrl string `json:"image_url" dc:"图片URL"`
	LinkUrl  string `json:"link_url" dc:"跳转链接"`
}

// 新增广告
type AdvertisementAddReq struct {
	g.Meta       `path:"/advertisement/add" tags:"广告管理" method:"post" summary:"新增广告"`
	PositionCode string                        `json:"position_code" v:"required#Please select advertisement position" dc:"广告位置编码"`
	Languages    []AdvertisementLanguageDetail `json:"languages" v:"required#Please set multi-language content" dc:"多语言内容"`
}

type AdvertisementAddRes struct {
	AdvertisementId int `json:"advertisement_id" dc:"新增广告ID"`
}

// 编辑广告
type AdvertisementEditReq struct {
	g.Meta          `path:"/advertisement/edit" tags:"广告管理" method:"post" summary:"编辑广告"`
	AdvertisementId int                           `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
	PositionCode    string                        `json:"position_code" v:"required#Please select advertisement position" dc:"广告位置编码"`
	Languages       []AdvertisementLanguageDetail `json:"languages" v:"required#Please set multi-language content" dc:"多语言内容"`
}

type AdvertisementEditRes struct {
	EmptyDataRes
}

// 删除广告
type AdvertisementDeleteReq struct {
	g.Meta          `path:"/advertisement/delete" tags:"广告管理" method:"post" summary:"删除广告"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
}

type AdvertisementDeleteRes struct {
	EmptyDataRes
}

// 切换广告状态
type AdvertisementToggleStatusReq struct {
	g.Meta          `path:"/advertisement/toggle-status" tags:"广告管理" method:"post" summary:"切换广告状态"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID"`
	Status          int `json:"status" v:"required|in:0,1#Please select status|Invalid status value" dc:"状态: 0-下线, 1-上线"`
}

type AdvertisementToggleStatusRes struct {
	EmptyDataRes
}

// 广告统计概览
type AdvertisementStatsOverviewReq struct {
	g.Meta          `path:"/advertisement/stats/overview" tags:"广告管理" method:"post" summary:"广告统计概览"`
	AdvertisementId int `json:"advertisement_id" v:"required|min:1#Please select advertisement|Invalid advertisement ID" dc:"广告ID，为空则统计所有广告"`
}

type AdvertisementStatsOverviewRes struct {
	TotalViews      int64 `json:"total_views" dc:"累计展示"`
	TotalClicks     int64 `json:"total_clicks" dc:"累计点击"`
	TodayViews      int64 `json:"today_views" dc:"今日展示"`
	TodayClicks     int64 `json:"today_clicks" dc:"今日点击"`
	YesterdayViews  int64 `json:"yesterday_views" dc:"昨日展示"`
	YesterdayClicks int64 `json:"yesterday_clicks" dc:"昨日点击"`
	ViewsGrowth     int64 `json:"views_growth" dc:"展示增长量（今日-昨日）"`
	ClicksGrowth    int64 `json:"clicks_growth" dc:"点击增长量（今日-昨日）"`
}

// 广告统计趋势图
type AdvertisementStatsTrendReq struct {
	g.Meta          `path:"/advertisement/stats/trend" tags:"广告管理" method:"post" summary:"广告统计趋势图"`
	AdvertisementId int     `json:"advertisement_id" dc:"广告ID，为空则统计所有广告"`
	DateRange       string  `json:"date_range" v:"required|in:7,30,custom#Please select date range|Invalid date range" dc:"时间范围: 7-近7天, 30-近30天, custom-自定义"`
	StartDate       *string `json:"start_date" v:"required-if:date_range,custom|date-format:2006-01-02#Start date is required for custom range|Invalid start date format" dc:"开始日期(YYYY-MM-DD)，自定义时间范围时必填"`
	EndDate         *string `json:"end_date" v:"required-if:date_range,custom|date-format:2006-01-02#End date is required for custom range|Invalid end date format" dc:"结束日期(YYYY-MM-DD)，自定义时间范围时必填"`
	DataType        string  `json:"data_type" v:"required|in:cumulative,daily#Please select data type|Invalid data type" dc:"数据类型: cumulative-累计数据, daily-每日数据"`
}

type AdvertisementStatsTrendRes struct {
	Labels []string                     `json:"labels" dc:"日期标签"`
	Data   []AdvertisementTrendDataItem `json:"data" dc:"趋势数据"`
}

type AdvertisementTrendDataItem struct {
	Date   string `json:"date" dc:"日期"`
	Views  int64  `json:"views" dc:"展示量"`
	Clicks int64  `json:"clicks" dc:"点击量"`
}
