package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ==================== 朝觐日程说明相关接口 ====================

// HajiJadwalDescriptionGetReq 获取日程说明请求
type HajiJadwalDescriptionGetReq struct {
	g.Meta `path:"/hajiJadwal/description/get" method:"post" tags:"朝觐日程" summary:"获取日程说明"`
}

type HajiJadwalDescriptionGetRes struct {
	Description []HajiJadwalDescriptionItem `json:"description" dc:"日程说明"`
}

// HajiJadwalDescriptionSaveReq 保存日程说明请求
type HajiJadwalDescriptionSaveReq struct {
	g.Meta      `path:"/hajiJadwal/description/save" method:"post" tags:"朝觐日程" summary:"保存日程说明"`
	Description []HajiJadwalDescriptionItem `v:"required|array|required" json:"description" dc:"日程说明内容"`
}

type HajiJadwalDescriptionItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言类型: 0-中文, 1-英文, 2-印尼语"`
	Description  string `v:"required" json:"description" dc:"日程说明"`
}

type HajiJadwalDescriptionSaveRes struct{}

// ==================== 朝觐日程相关接口 ====================

// HajiJadwalListReq 日程列表请求
type HajiJadwalListReq struct {
	g.Meta `path:"/hajiJadwal/list" method:"post" tags:"朝觐日程" summary:"日程列表"`
	ListReq
}

type HajiJadwalListItem struct {
	Id           uint64            `json:"id" dc:"日程ID"`
	ItemNo       int               `json:"item_no" dc:"展示顺序"`
	TimeInfo     string            `json:"time_info" dc:"日程时间"`
	EventSummary string            `json:"event_summary" dc:"日程标题"`
	LanguageArr  []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
}

type HajiJadwalListRes struct {
	List []HajiJadwalListItem `json:"list" dc:"日程列表"`
	ListRes
}

// HajiJadwalCreateReq 新增日程请求
type HajiJadwalCreateReq struct {
	g.Meta  `path:"/hajiJadwal/add" method:"post" tags:"朝觐日程" summary:"新增日程"`
	ItemNo  int                    `v:"required|min:1|max:9999" json:"item_no" dc:"展示顺序"`
	Content []HajiJadwalCreateItem `v:"required|array|required" json:"content" dc:"日程内容"`
}

type HajiJadwalCreateItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TimeInfo     string `v:"required" json:"time_info" dc:"日程时间"`
	EventSummary string `v:"required" json:"event_summary" dc:"日程标题"`
	ArticleText  string `v:"required" json:"article_text" dc:"日程内容（富文本）"`
}

type HajiJadwalCreateRes struct {
	Id int64 `json:"id" dc:"日程ID"`
}

// HajiJadwalEditReq 编辑日程请求
type HajiJadwalEditReq struct {
	g.Meta  `path:"/hajiJadwal/edit" method:"post" tags:"朝觐日程" summary:"编辑日程"`
	Id      int64                `v:"required" json:"id" dc:"日程ID"`
	ItemNo  int                  `v:"required|min:1|max:9999" json:"item_no" dc:"展示顺序"`
	Content []HajiJadwalEditItem `v:"required|array|required" json:"content" dc:"日程内容"`
}

type HajiJadwalEditItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TimeInfo     string `v:"required" json:"time_info" dc:"日程时间"`
	EventSummary string `v:"required" json:"event_summary" dc:"日程标题"`
	ArticleText  string `v:"required" json:"article_text" dc:"日程内容（富文本）"`
}

type HajiJadwalEditRes struct{}

// HajiJadwalOneReq 获取单个日程请求
type HajiJadwalOneReq struct {
	g.Meta `path:"/hajiJadwal/one" method:"post" tags:"朝觐日程" summary:"获取单个日程"`
	Id     int64 `v:"required" json:"id" dc:"日程ID"`
}

type HajiJadwalOneRes struct {
	Id      int64                `json:"id" dc:"日程ID"`
	ItemNo  int                  `json:"item_no" dc:"展示顺序"`
	Content []HajiJadwalEditItem `json:"content" dc:"日程内容"`
}

// HajiJadwalDeleteReq 删除日程请求
type HajiJadwalDeleteReq struct {
	g.Meta `path:"/hajiJadwal/delete" method:"post" tags:"朝觐日程" summary:"删除日程"`
	Id     int64 `v:"required" json:"id" dc:"日程ID"`
}

type HajiJadwalDeleteRes struct{}
