package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	entity "gtcms/internal/model/entity/user_account_svc"
)

type UserNuListReq struct {
	g.Meta      `path:"/userUn/list" method:"post" tags:"Nu认证" summary:"Nu认证列表"`
	RealName    string `json:"realName"         description:"用户真实姓名"`
	NuIdNumber  string `json:"nuIdNumber"    description:"NU会员编号，例如 NU-2024-JAWA-001"`
	NickName    string `json:"nickName" description:"昵称"`
	UserId      uint64 `json:"userId"           description:"用户ID"`
	Gender      *int   `json:"gender"            description:"性别：0未知 1男 2女"`
	Status      int    `json:"status"              description:"认证状态：1待审核 2已通过 3已驳回"`
	WilayahPwnu string `json:"wilayahPwnu"   description:"所属PWNU地区,nu地址，从nu_wilayah_pwnu选择"`
	StartTime   int64  `json:"startTime"      description:"开始时间"`
	EndTime     int64  `json:"endTime"          description:"结束时间"`
	ListReq
}

type UserNuListRes struct {
	ListRes
	List []*UserNuListItem `json:"list" description:"列表"`
}

type UserNuListItem struct {
	entity.UserNuId
	Nickname string `json:"nickname"             orm:"nickname"                description:"昵称"`
}

type UserNuAuditReq struct {
	g.Meta      `path:"/userUn/audit" method:"post" tags:"Nu认证" summary:"Nu认证审核"`
	Ids         []uint64 `v:"required|array" json:"ids"                   description:"ID"`
	AuditStatus int      `v:"required|in:2,3" json:"auditStatus" description:"审核状态：1待审核 2通过 3驳回"`
	AuditReason string   `v:"required-if:AuditStatus,3|max-length:255" json:"auditReason" description:"审核原因 字符串"`
}

type UserNuAuditRes struct {
}

type UserNuOneReq struct {
	g.Meta `path:"/userUn/one" method:"post" tags:"Nu认证" summary:"Nu认证详情"`
	Id     uint64 `v:"required" json:"id"                   description:"ID"`
}

type UserNuOneRes struct {
	UserNuListItem
	Audit *entity.UserNuAudit `json:"audit"`
}
