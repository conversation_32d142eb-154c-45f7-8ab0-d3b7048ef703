package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type UserListReq struct {
	g.<PERSON>a `path:"/user/list" method:"post" tags:"用户管理" summary:"列表"`
	ListReq
	//user 表
	Id              uint64 `json:"id"               orm:"id"                description:"用户UID"`
	IsBanned        int    `json:"isBanned"         orm:"is_banned"         description:"账号封号状态： 1 正常 2 封号"`
	PhoneNum        string `json:"phoneNum"         orm:"phone_num"         description:"手机号"`
	CreateTimeStart int64  `json:"createTimeStart"           description:"创建时间-开始（注册时间）"`
	CreateTimeEnd   int64  `json:"createTimeEnd"           description:"创建时间-结束（注册时间）"`
	//user-info 表
	IsNu         int    `json:"isNu"         orm:"is_nu"         description:"NU实名 0-未实名 1-已实名"`
	FirstName    string `json:"firstName"            orm:"first_name"              description:"第一个名字"`
	Email        string `json:"email"                orm:"email"                   description:"邮箱地址"`
	Gender       string `json:"gender"               orm:"gender"                  description:"性别：0未知 1男 2女"`
	Nickname     string `json:"nickname"             orm:"nickname"                description:"昵称"`
	LastSigninIp string `json:"lastSigninIp"         orm:"last_signin_ip"          description:"最后登录ip"`
}

type UserListRes struct {
	ListRes
	List []*UserVo `json:"list"`
}
type UserVo struct {
	Id         uint64 `json:"id"               orm:"id"                description:""`
	Account    string `json:"account"          orm:"account"           description:"账号"`
	PhoneNum   string `json:"phoneNum"         orm:"phone_num"         description:"手机号"`
	IsBanned   int    `json:"isBanned"         orm:"is_banned"         description:"账号封号状态： 1 正常 2 封号"`
	CreateTime int64  `json:"createTime"       orm:"create_time"       description:"创建时间（注册时间）"`
	IsNu       int    `json:"isNu"         orm:"is_nu"         description:"NU实名 0-未实名 1-已实名"`

	//info entity.UserInfo `json:"info" description:"用户信息"`
	FirstName  string `json:"firstName"            orm:"first_name"              description:"第一个名字"`
	MiddleName string `json:"middleName"           orm:"middle_name"             description:"中间名字"`
	LastName   string `json:"lastName"             orm:"last_name"               description:"最后一个名字"`

	Email          string `json:"email"                orm:"email"                   description:"邮箱地址"`
	CountryId      uint   `json:"countryId"            orm:"country_id"              description:"国家id"`
	Avatar         string `json:"avatar"               orm:"avatar"                  description:"头像url"`
	Gender         string `json:"gender"               orm:"gender"                  description:"性别：0未知 1男 2女"`
	Nickname       string `json:"nickname"             orm:"nickname"                description:"昵称"`
	Address        string `json:"address"              orm:"address"                 description:"住址"`
	DataType       int    `json:"dataType"             orm:"data_type"               description:"数据类型:1正式数据;2测试数据"`
	Source         int    `json:"source"               orm:"source"                  description:"注册来源( 1直客，2代理，3邀请，4后台）"`
	LastSigninTime int64  `json:"lastSigninTime"       orm:"last_signin_time"        description:"最后一次登录时间"`
	LastSigninIp   string `json:"lastSigninIp"         orm:"last_signin_ip"          description:"最后登录ip"`
}

type UserInfoReq struct {
	g.Meta `path:"/user/info" method:"post" tags:"用户管理" summary:"详情"`
	Id     uint64 `json:"id"               orm:"id"                description:"用户UID"`
}

type BatchBanReq struct {
	g.Meta `path:"/user/batchBan" method:"post" tags:"批量封禁" summary:"批量封禁"`
	Ids    []uint `json:"ids" dc:"id数组" v:"required"`
	Status uint64 `json:"status"               orm:"status"                description:"账号封号状态： 1 正常 2 封号"`
}

type UserLoginLogReq struct {
	g.Meta `path:"/user/loginLog" method:"post" tags:"登陆日志" summary:"登陆日志"`
	ListReq
	Id        uint64 `json:"id"               orm:"id"                description:"用户UID"`
	TimeStart int64  `json:"timeStart"           description:"开始时间"`
	TimeEnd   int64  `json:"timeEnd"           description:"结束时间"`
}

type UserLoginLogRes struct {
	ListRes
	List []*UserLoginLogVo `json:"list"`
}

type UserLoginLogVo struct {
	Id              uint64 `json:"bigint unsigned"               orm:"id"                description:""`
	SigninTime      string `json:"signin_time"         orm:"signin_time"          description:"登录时间"`
	Ip              string `json:"ip"         orm:"ip"          description:"登录ip（ip6长度为39字符）"`
	IpRegion        string `json:"ip_region"         orm:"ip_region"          description:"ip地址位置"`
	DeviceId        string `json:"device_id"         orm:"device_id"          description:"设备编号"`
	DeviceOs        string `json:"device_os"         orm:"device_os"          description:"设备系统（ios，android，mac，windows，。。。）"`
	DeviceOsVersion string `json:"device_os_version"         orm:"device_os_version"          description:"设备系统版本号"`
	DeviceType      string `json:"device_type"         orm:"device_type"          description:"设备类型（mobile手机，desktop台式，pad平板，。。。其他）"`
	AppType         string `json:"app_type"         orm:"app_type"          description:"应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	AppVersion      string `json:"app_version"         orm:"app_version"          description:"应用版本号"`
}
type UserInfoRes struct {
	Id         uint64 `json:"id"               orm:"id"                description:""`
	Account    string `json:"account"          orm:"account"           description:"账号"`
	PhoneNum   string `json:"phoneNum"         orm:"phone_num"         description:"手机号"`
	CreateTime int64  `json:"createTime"       orm:"create_time"       description:"创建时间（注册时间）"`
	IsNu       int    `json:"isNu"         orm:"is_nu"         description:"NU实名 0-未实名 1-已实名"`
	//info entity.UserInfo `json:"info" description:"用户信息"`
	FirstName  string `json:"firstName"            orm:"first_name"              description:"第一个名字"`
	MiddleName string `json:"middleName"           orm:"middle_name"             description:"中间名字"`
	LastName   string `json:"lastName"             orm:"last_name"               description:"最后一个名字"`

	Email          string `json:"email"                orm:"email"                   description:"邮箱地址"`
	CountryId      uint   `json:"countryId"            orm:"country_id"              description:"国家id"`
	Avatar         string `json:"avatar"               orm:"avatar"                  description:"头像url"`
	Gender         string `json:"gender"               orm:"gender"                  description:"性别：0未知 1男 2女"`
	Nickname       string `json:"nickname"             orm:"nickname"                description:"昵称"`
	Address        string `json:"address"              orm:"address"                 description:"住址"`
	DataType       int    `json:"dataType"             orm:"data_type"               description:"数据类型:1正式数据;2测试数据"`
	Source         int    `json:"source"               orm:"source"                  description:"注册来源( 1直客，2代理，3邀请，4后台）"`
	LastSigninTime int64  `json:"lastSigninTime"       orm:"last_signin_time"        description:"最后一次登录时间"`
	YearOfBirth    int    `json:"yearOfBirth"          orm:"year_of_birth"           description:"出生年"`
	MonthOfBirth   int    `json:"monthOfBirth"         orm:"month_of_birth"          description:"出生月"`
	DayOfBirth     int    `json:"dayOfBirth"           orm:"day_of_birth"            description:"出生日"`
	LastSigninIp   string `json:"lastSigninIp"         orm:"last_signin_ip"          description:"最后登录ip"`

	CollectCounts int `json:"collectCounts"             orm:"collectCounts"               description:"收藏次数"`
	ViewCounts    int `json:"viewCounts"             orm:"viewCounts"               description:"浏览次数"`
	ShareCounts   int `json:"shareCounts"             orm:"shareCounts"               description:"分享次数"`
	LoginCounts   int `json:"loginCounts"             orm:"loginCounts"               description:"登录次数"`

	AddressInfo []*AddressInfo `json:"addressInfo" description:"地址信息"`
}
type AddressInfo struct {
	Level1Code string `json:"level1Code" orm:"level1_code" description:"省级代码"`
	Level1Name string `json:"level1Name" orm:"level1_name" description:"省名称"`
	Level2Code string `json:"level2Code" orm:"level2_code" description:"市级代码"`
	Level2Name string `json:"level2Name" orm:"level2_name" description:"市名称"`
	Level3Code string `json:"level3Code" orm:"level3_code" description:"区/镇代码"`
	Level3Name string `json:"level3Name" orm:"level3_name" description:"区/镇名称"`
	Level4Code string `json:"level4Code" orm:"level4_code" description:"村/社区代码"`
	Level4Name string `json:"level4Name" orm:"level4_name" description:"村/社区名称"`
	Receiver   string `json:"receiver"          orm:"receiver"           description:"收货人姓名"`
	PhoneNum   string `json:"phoneNum"         orm:"phone_num"         description:"手机号"`
	Address    string `json:"address"              orm:"address"                 description:"住址"`
	PostCode   string `json:"postCode"              orm:"postCode"                 description:"邮政编码"`
	IsDefault  int    `json:"isDefault"         orm:"isDefault"         description:"是否默认地址 1是 2否"`
}

type CollectListReq struct {
	g.Meta `path:"/user/collectList" method:"post" tags:"用户管理" summary:"收藏列表"`
	ListReq
	Id uint64 `json:"id"               orm:"id"                description:"用户UID"`

	Type            string `json:"type"         orm:"type"         description:"内容类型：activity-活动，article-文章，quran-古兰经，doa-祷告"`
	CreateTimeStart int64  `json:"createTimeStart"           description:"创建时间-开始（注册时间）"`
	CreateTimeEnd   int64  `json:"createTimeEnd"           description:"创建时间-结束（注册时间）"`
}

type ReadListReq struct {
	g.Meta `path:"/user/readList" method:"post" tags:"用户管理" summary:"阅读列表"`
	ListReq
	Id              uint64 `json:"id"               orm:"id"                description:"用户UID"`
	Type            string `json:"type"         orm:"type"         description:"内容类型：activity-活动，article-文章，quran-古兰经，doa-祷告"`
	CreateTimeStart int64  `json:"createTimeStart"           description:"创建时间-开始（注册时间）"`
	CreateTimeEnd   int64  `json:"createTimeEnd"           description:"创建时间-结束（注册时间）"`
}

type ShareListReq struct {
	g.Meta `path:"/user/shareList" method:"post" tags:"用户管理" summary:"分享列表"`
	ListReq
	Id              uint64 `json:"id"               orm:"id"                description:"用户UID"`
	Type            string `json:"type"         orm:"type"         description:"内容类型：activity-活动，article-文章，quran-古兰经，doa-祷告"`
	CreateTimeStart int64  `json:"createTimeStart"           description:"创建时间-开始（注册时间）"`
	CreateTimeEnd   int64  `json:"createTimeEnd"           description:"创建时间-结束（注册时间）"`
}

type RecordListRes struct {
	ListRes
	List []*RecordListVo `json:"list"`
}
type RecordListVo struct {
	Id         uint64 `json:"id"               orm:"id"                description:""`
	CreateTime int64  `json:"createTime"       orm:"create_time"       description:"创建时间（注册时间）"`
	Type       string `json:"type"         orm:"type"         description:"内容类型：activity-活动，article-文章，quran-古兰经，doa-祷告"`
	Title      string `json:"title"         orm:"title"         description:"内容标题"`
}

//
//type AccountDeleteReq struct {
//	g.Meta `path:"/accountMgr/delete" method:"post" tags:"账户管理" summary:"删除"`
//	Id     uint `v:"required" json:"id" dc:"管理员id"`
//}
//
//type AccountEditReq struct {
//	g.Meta   `path:"/accountMgr/edit" method:"post" tags:"账户管理" summary:"编辑"`
//	Id       uint    `v:"required" json:"id"`
//	Account  string  `v:"required|passport|length:6,18#admin.account.required|gf.gvalid.rule.passport|admin.account.length" json:"account" dc:"账户"`
//	Password *string `v:"password3" json:"password" dc:"密码"`
//	RoleId   uint    `json:"roleId" dc:"角色id"`
//	NickName string  `json:"nickName"      dc:"昵称"`
//	//Contact       string  `json:"contact"       dc:"联系方式"`
//	Remark string `json:"remark"        dc:"备注"`
//	//AuditPassword *string `v:"length:6,6"  json:"auditPassword" dc:"私人密码"`
//	IsAffect            int `v:"between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
//	IsRequireGoogleAuth int `json:"isRequireGoogleAuth" description:"谷歌验证码登录开关 1:需要 2:不用"`
//}
//
//type AccountDetailReq struct {
//	g.Meta `path:"/accountMgr/detail" method:"post" tags:"账户管理" summary:"详情"`
//	Id     uint `v:" " json:"id"`
//}
//type AccountDetailRes struct {
//	AccountVo
//}
//
//type AccountSetStatusReq struct {
//	g.Meta   `path:"/accountMgr/setStatus" method:"post" tags:"账户管理" summary:"启用设置"`
//	Id       uint `v:"required" json:"id"`
//	IsAffect int  `v:"required|between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
//	//AuditPassword string `v:"required|length:6,6"  json:"auditPassword" dc:"私人密码"`
//}
//
//type AccountListReq struct {
//	g.Meta `path:"/accountMgr/list" method:"post" tags:"账户管理" summary:"列表"`
//	ListReq
//	RoleId    *uint   `json:"roleId" dc:"角色ID"`
//	Account   *string `json:"account" dc:"账户名称"`
//	StartTime int64   `json:"startTime" dc:"开始时间"`
//	EndTime   int64   `json:"endTime" dc:"结束时间"`
//}

//
//type AccountVo struct {
//	Id      uint   `json:"id"            description:""`
//	Account string `json:"account"       description:"帐号"`
//	//Password       string `json:"password"      description:"密码"`
//	GoogleAuthBind      int    `json:"googleAuthBind"      description:"谷歌登录 1:已绑定 2:未绑定"`
//	NickName            string `json:"nickName"      description:"昵称"`
//	Contact             string `json:"contact"       description:"联系方式"`
//	Remark              string `json:"remark"        description:"备注"`
//	RoleId              uint   `json:"roleId"        description:"角色id"`
//	RoleName            string `json:"roleName"        description:"角色名称"`
//	AuditPassword       string `json:"auditPassword" description:"私人密码"`
//	IsOnline            int    `json:"isOnline"      description:"1:在线 2:离线"`
//	IsAffect            int    `json:"isAffect"      description:"1:启用 2:停用"`
//	CreateTime          int64  `json:"createTime"    description:"创建时间"`
//	CreateAccount       string `json:"createAccount" description:"创建者"`
//	UpdateTime          int64  `json:"updateTime"    description:"更新时间"`
//	UpdateAccount       string `json:"updateAccount" description:"更新者"`
//	Creater             uint   `json:"creater"        description:"创建者"`
//	IsRequireGoogleAuth int    `json:"isRequireGoogleAuth" description:"谷歌开关 1:需要 2:不用"`
//	GoogleAuthSecret    string `json:"googleAuthSecret"    description:"谷歌验证秘钥"`
//	TemplateIds         []uint `json:"templateIds" description:"模板id集合"`
//}
//
//type AccountNameReq struct {
//	g.Meta `path:"/accountMgr/name" method:"post" tags:"账户管理" summary:"Creator名称"`
//}
//type AccountNameRes struct {
//	MapIdName map[uint]string `json:"mapIdName" dc:"账号ID名称对应表"`
//}
//
//type AccountSetGoogleAuthReq struct {
//	g.Meta   `path:"/accountMgr/setGoogleAuth" method:"post" tags:"账户管理" summary:"谷歌开关设置"`
//	Id       uint `v:"required" json:"id"`
//	IsAffect int  `v:"required|between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
//}
//
//type AccountDetectionReq struct {
//	g.Meta  `path:"/accountMgr/detection" method:"post" tags:"管理员操作" summary:"账号检测"`
//	Account string `v:"required" json:"account"`
//}
//type AccountDetectionRes struct {
//	IsExistGoogleOtp bool `json:"isExistGoogleOtp" dc:"谷歌验证码是否存在：ture是, false否"`
//}
//
//type AccountSetTemplateReq struct {
//	g.Meta      `path:"/accountMgr/setTmpl" method:"post" tags:"管理员操作" summary:"模板设置"`
//	Id          uint    `v:"required" json:"id"`
//	TemplateIds *[]uint `json:"templateIds" dc:"模板id集合"`
//}
