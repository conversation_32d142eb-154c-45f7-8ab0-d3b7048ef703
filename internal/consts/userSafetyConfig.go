package consts

// 类型
const (
	ConfigTypeSms           = iota // 短信及WhatsApp验证码机制
	ConfigTypeSensitiveWord        // 敏感词过滤
)

// 配置键
const (
	MobileInterval        = "mobile_interval"          // 同一手机号发送间隔(秒)
	MobileIntervalMaxSend = "mobile_interval_max_send" // 同一手机号间隔内最多发送次数
	MobileDailyLimit      = "mobile_daily_limit"       // 单手机号每日限额
	IpHourlyMobileLimit   = "ip_hourly_mobile_limit"   // 单IP每小时手机号数量限额
	IpDailySendLimit      = "ip_daily_send_limit"      // 单IP每日发送数量限额
	MobileIpMinute        = "mobile_ip_minute"         // 单手机号IP时间范围（分钟）
	MobileIpMaxRetry      = "mobile_ip_max_retry"      // 单手机号IP最大重试次数
)

// 配置描述
const (
	MobileIntervalDesc         = "同一手机号发送间隔(秒)"
	MobileIntervalMaxSendDesc  = "同一手机号间隔内最多发送次数"
	MobileDailyLimitDesc       = "单手机号每日限额"
	IpHourlyMobileLimitDesc    = "单IP每小时手机号数量限额"
	IpDailySendLimitDesc       = "单IP每日发送数量限额"
	MobileIpMinuteDesc         = "单手机号IP时间范围（分钟）"
	MobileIpMaxRetryDesc       = "单手机号IP最大重试次数"
	SensitiveWordFilteringDesc = "敏感词过滤"
)

func GetConfigDesc(key string) string {
	return map[string]string{
		MobileInterval:         MobileIntervalDesc,
		MobileIntervalMaxSend:  MobileIntervalMaxSendDesc,
		MobileDailyLimit:       MobileDailyLimitDesc,
		IpHourlyMobileLimit:    IpHourlyMobileLimitDesc,
		IpDailySendLimit:       IpDailySendLimitDesc,
		MobileIpMinute:         MobileIpMinuteDesc,
		MobileIpMaxRetry:       MobileIpMaxRetryDesc,
		SensitiveWordFiltering: SensitiveWordFilteringDesc,
	}[key]
}

// 敏感词过滤
const (
	SensitiveWordFiltering = "sensitive_word_filtering" // 敏感词过滤
)

const (
	SensitiveWordFilteringClose = iota // 关闭
	SensitiveWordFilteringOpen         // 开启
)

// 敏感次匹配类型
const (
	SensitiveWordMatchTypeAll   = iota // 全匹配
	SensitiveWordMatchTypeLike         // 模糊匹配
	SensitiveWordMatchTypeRegex        // 正则匹配
)

const (
	SensitiveWordMatchTypeAllText   = "全匹配"
	SensitiveWordMatchTypeLikeText  = "模糊匹配"
	SensitiveWordMatchTypeRegexText = "正则匹配"
)

func SensitiveWordMatchTypeName(key int) string {
	return map[int]string{
		SensitiveWordMatchTypeAll:   SensitiveWordMatchTypeAllText,
		SensitiveWordMatchTypeLike:  SensitiveWordMatchTypeLikeText,
		SensitiveWordMatchTypeRegex: SensitiveWordMatchTypeRegexText,
	}[key]
}
