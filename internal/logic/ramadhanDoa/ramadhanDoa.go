package ramadhanDoa

type sRamadhanDoa struct{}

func init() {
	//service.RegisterRamadhanDoa(New())
}

func New() *sRamadhanDoa {
	return &sRamadhanDoa{}
}

// 列表
//func (s *sRamadhanDoa) List(ctx context.Context, req *v1.RamadhanDoaListReq) (out *v1.RamadhanDoaListRes, err error) {
//	out = &v1.RamadhanDoaListRes{}
//	out.Current = req.Current
//	out.Offset = req.Offset
//	var list []entity.RamadhanDoa
//	orm := dao.RamadhanDoa.Ctx(ctx)
//	// 获取总数
//	out.Total, err = orm.Count()
//	if err != nil {
//		out.List = []v1.RamadhanDoaListItem{}
//		return out, err
//	}
//	if out.Total <= 0 {
//		out.List = []v1.RamadhanDoaListItem{}
//		return out, nil
//	}
//	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.RamadhanDoa.Columns().DoaNo).Scan(&list)
//	if err != nil && !errors.Is(err, sql.ErrNoRows) {
//		return
//	}
//	// 提取id
//	doaIds := gutil.ListItemValuesUnique(list, "Id")
//	// 获取原文数据
//	var doaTextList []entity.RamadhanDoaContent
//	err = dao.RamadhanDoaContent.Ctx(ctx).
//		WhereIn(dao.RamadhanDoaContent.Columns().DoaId, doaIds).
//		Scan(&doaTextList)
//	if err != nil && !errors.Is(err, sql.ErrNoRows) {
//		return
//	}
//	doaTextMap := make(map[int]entity.RamadhanDoaContent)
//	for _, v := range doaTextList {
//		doaTextMap[gconv.Int(v.DoaId)] = v
//	}
//	for _, doa := range list {
//		doaContent, ok := doaTextMap[gconv.Int(doa.Id)]
//		one := v1.RamadhanDoaListItem{
//			Id:       gconv.Uint(doa.Id),
//			DoaTitle: doa.DoaName,
//			DoaNo:    doa.DoaNo,
//		}
//		if ok {
//			one.ArabicText = doaContent.ArabicText
//			one.IndonesianText = doaContent.IndonesianText
//			one.LatinText = doaContent.LatinText
//		}
//		out.List = append(out.List, one)
//	}
//	return out, err
//}
