package hajiNewsTag

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
)

type sHajiNewsTag struct{}

func init() {
	service.RegisterHajiNewsTag(New())
}

func New() *sHajiNewsTag {
	return &sHajiNewsTag{}
}

// Add 新增朝觐新闻标签
func (s *sHajiNewsTag) Add(ctx context.Context, req *v1.HajiNewsTagAddReq) (err error) {
	if len(req.NameArr) == 0 {
		return gerror.New("标签名称不能为空")
	}

	// 检查标签名称是否重复
	// tag不会很多，循环里查一下没什么关系
	for _, item := range req.NameArr {
		count, err := dao.HajiNewsTagLanguages.Ctx(ctx).
			Where(dao.HajiNewsTagLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiNewsTagLanguages.Columns().TagName, item.TagName).
			Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.New("该标签名称已存在")
		}
	}

	var tagId int64
	err = dao.HajiNewsTag.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		tagId, err1 = tx.Model(dao.HajiNewsTag.Table()).Ctx(ctx).
			Data(do.HajiNewsTag{
				SortOrder:  req.SortOrder,
				CreateTime: gtime.Now().UnixMilli(),
				UpdateTime: gtime.Now().UnixMilli(),
			}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言表
		var languages []do.HajiNewsTagLanguages
		for _, item := range req.NameArr {
			languages = append(languages, do.HajiNewsTagLanguages{
				TagId:      tagId,
				LanguageId: item.LanguageType,
				TagName:    item.TagName,
				CreateTime: gtime.Now().UnixMilli(),
				UpdateTime: gtime.Now().UnixMilli(),
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiNewsTagLanguages.Table()).Ctx(ctx).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		// 插入文章关联
		if len(req.ArticleArr) > 0 {
			var articles []do.HajiNewsTagArticle
			for _, articleId := range req.ArticleArr {
				articles = append(articles, do.HajiNewsTagArticle{
					TagId:      tagId,
					ArticleId:  articleId,
					CreateTime: gtime.Now().UnixMilli(),
					UpdateTime: gtime.Now().UnixMilli(),
				})
			}

			_, err1 = tx.Model(dao.HajiNewsTagArticle.Table()).Ctx(ctx).
				Data(articles).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	return err
}

// Edit 编辑朝觐新闻标签
func (s *sHajiNewsTag) Edit(ctx context.Context, req *v1.HajiNewsTagEditReq) (err error) {
	if len(req.NameArr) == 0 {
		return gerror.New("标签名称不能为空")
	}

	// 检查标签是否存在
	count, err := dao.HajiNewsTag.Ctx(ctx).
		Where(dao.HajiNewsTag.Columns().Id, req.Id).
		Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("标签不存在")
	}

	// 检查标签名称是否重复（排除自己）
	// tag不会很多，循环里查一下没什么关系
	for _, item := range req.NameArr {
		count, err := dao.HajiNewsTagLanguages.Ctx(ctx).
			Where(dao.HajiNewsTagLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiNewsTagLanguages.Columns().TagName, item.TagName).
			WhereNot(dao.HajiNewsTagLanguages.Columns().TagId, req.Id).
			Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.New("该标签名称已存在")
		}
	}

	err = dao.HajiNewsTag.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = tx.Model(dao.HajiNewsTag.Table()).Ctx(ctx).
			Data(do.HajiNewsTag{
				SortOrder:  req.SortOrder,
				UpdateTime: gtime.Now().UnixMilli(),
			}).
			Where(dao.HajiNewsTag.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的多语言记录
		_, err1 = tx.Model(dao.HajiNewsTagLanguages.Table()).Ctx(ctx).
			Where(dao.HajiNewsTagLanguages.Columns().TagId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言记录
		var languages []do.HajiNewsTagLanguages
		for _, item := range req.NameArr {
			languages = append(languages, do.HajiNewsTagLanguages{
				TagId:      req.Id,
				LanguageId: item.LanguageType,
				TagName:    item.TagName,
				CreateTime: gtime.Now().UnixMilli(),
				UpdateTime: gtime.Now().UnixMilli(),
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiNewsTagLanguages.Table()).Ctx(ctx).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		// 删除旧的文章关联
		_, err1 = tx.Model(dao.HajiNewsTagArticle.Table()).Ctx(ctx).
			Where(dao.HajiNewsTagArticle.Columns().TagId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的文章关联
		if len(req.ArticleArr) > 0 {
			var articles []do.HajiNewsTagArticle
			for _, articleId := range req.ArticleArr {
				articles = append(articles, do.HajiNewsTagArticle{
					TagId:      req.Id,
					ArticleId:  articleId,
					CreateTime: gtime.Now().UnixMilli(),
					UpdateTime: gtime.Now().UnixMilli(),
				})
			}

			_, err1 = tx.Model(dao.HajiNewsTagArticle.Table()).Ctx(ctx).
				Data(articles).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	return err
}

// Info 获取朝觐新闻标签详情
func (s *sHajiNewsTag) Info(ctx context.Context, req *v1.HajiNewsTagInfoReq) (out *v1.HajiNewsTagInfoRes, err error) {
	out = new(v1.HajiNewsTagInfoRes)

	// 获取标签基本信息
	var tag entity.HajiNewsTag
	err = dao.HajiNewsTag.Ctx(ctx).
		Where(dao.HajiNewsTag.Columns().Id, req.Id).
		Scan(&tag)
	if err != nil {
		return nil, err
	}
	if tag.Id == 0 {
		return nil, gerror.New("标签不存在")
	}

	// 获取多语言信息
	var languages []entity.HajiNewsTagLanguages
	err = dao.HajiNewsTagLanguages.Ctx(ctx).
		Where(dao.HajiNewsTagLanguages.Columns().TagId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 获取关联文章
	var articles []entity.HajiNewsTagArticle
	err = dao.HajiNewsTagArticle.Ctx(ctx).
		Where(dao.HajiNewsTagArticle.Columns().TagId, req.Id).
		Scan(&articles)
	if err != nil {
		return nil, err
	}

	// 组装返回数据
	out.Id = int(tag.Id)
	out.SortOrder = int(tag.SortOrder)

	// 组装多语言数据
	for _, lang := range languages {
		out.NameArr = append(out.NameArr, v1.HajiNewsTagNameArrItem{
			LanguageType: int(lang.LanguageId),
			TagName:      lang.TagName,
		})
	}

	// 组装文章数据
	for _, article := range articles {
		out.ArticleArr = append(out.ArticleArr, int(article.ArticleId))
	}

	// 根据文章ID查询对应的分类ID
	if len(out.ArticleArr) > 0 {
		var newsArticles []entity.NewsArticle
		err = dao.NewsArticle.Ctx(ctx).
			WhereIn(dao.NewsArticle.Columns().Id, out.ArticleArr).
			Fields(dao.NewsArticle.Columns().Id, dao.NewsArticle.Columns().CategoryId).
			Scan(&newsArticles)
		if err != nil {
			return nil, err
		}

		out.ArticleCategoryArr = gconv.Ints(gutil.ListItemValuesUnique(newsArticles, "CategoryId"))
	}

	return out, nil
}

// List 获取朝觐新闻标签列表
func (s *sHajiNewsTag) List(ctx context.Context, req *v1.HajiNewsTagListReq) (out *v1.HajiNewsTagListRes, err error) {
	out = new(v1.HajiNewsTagListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Uint(ctx.Value(consts.LanguageId))

	query := dao.HajiNewsTag.Ctx(ctx).OrderAsc(dao.HajiNewsTag.Columns().SortOrder)

	total, err := query.Count()
	if err != nil {
		return nil, err
	}
	out.Total = total

	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	var tags []entity.HajiNewsTag
	err = query.Scan(&tags)
	if err != nil {
		return nil, err
	}

	if len(tags) == 0 {
		out.List = []v1.HajiNewsTagListItem{}
		return out, nil
	}

	// 获取所有标签的多语言信息
	tagIds := gutil.ListItemValuesUnique(tags, "Id")
	var allLanguages []entity.HajiNewsTagLanguages
	err = dao.HajiNewsTagLanguages.Ctx(ctx).
		WhereIn(dao.HajiNewsTagLanguages.Columns().TagId, tagIds).
		Scan(&allLanguages)
	if err != nil {
		return nil, err
	}

	// 按标签ID分组多语言信息
	languageMap := make(map[uint64][]entity.HajiNewsTagLanguages)
	for _, lang := range allLanguages {
		languageMap[lang.TagId] = append(languageMap[lang.TagId], lang)
	}

	// 获取文章关联数量
	var articleCounts []struct {
		TagId uint64 `json:"tag_id"`
		Count int    `json:"count"`
	}
	err = dao.HajiNewsTagArticle.Ctx(ctx).
		Fields("tag_id, COUNT(*) as count").
		WhereIn(dao.HajiNewsTagArticle.Columns().TagId, tagIds).
		Group(dao.HajiNewsTagArticle.Columns().TagId).
		Scan(&articleCounts)
	if err != nil {
		return nil, err
	}

	// 按标签ID分组文章数量
	articleCountMap := make(map[uint64]int)
	for _, count := range articleCounts {
		articleCountMap[count.TagId] = count.Count
	}

	// 组装返回数据
	for _, tag := range tags {
		item := v1.HajiNewsTagListItem{
			Id:           int(tag.Id),
			SortOrder:    int(tag.SortOrder),
			ArticleCount: articleCountMap[tag.Id],
			CreateTime:   int64(tag.CreateTime),
		}

		// 设置当前语言的标签名称
		languages := languageMap[tag.Id]
		for _, lang := range languages {
			if lang.LanguageId == uint(languageId) {
				item.TagName = lang.TagName
				break
			}
		}

		// 构建语言支持列表
		item.LanguageArr = s.buildLanguageArr(languages)

		out.List = append(out.List, item)
	}

	return out, nil
}

// buildLanguageArr 构建语言支持列表
func (s *sHajiNewsTag) buildLanguageArr(languages []entity.HajiNewsTagLanguages) []v1.LanguageArrItem {
	languageArr := []v1.LanguageArrItem{
		{LanguageType: consts.Zero, LanguageTypeText: "ZH", IsSupport: consts.Zero},
		{LanguageType: consts.One, LanguageTypeText: "EN", IsSupport: consts.Zero},
		{LanguageType: consts.Two, LanguageTypeText: "ID", IsSupport: consts.Zero},
	}

	for _, lang := range languages {
		if int(lang.LanguageId) < len(languageArr) {
			languageArr[lang.LanguageId].IsSupport = consts.One
		}
	}

	return languageArr
}

// Delete 删除朝觐新闻标签
func (s *sHajiNewsTag) Delete(ctx context.Context, req *v1.HajiNewsTagDeleteReq) (err error) {
	if req.Id == 0 {
		return gerror.New("请选择要删除的标签")
	}

	// 检查标签是否存在关联文章
	count, err1 := dao.HajiNewsTagArticle.Ctx(ctx).
		Where(dao.HajiNewsTagArticle.Columns().TagId, req.Id).
		Count()
	if err1 != nil {
		return err1
	}
	if count > 0 {
		return gerror.New("标签有文章关联，不能删除")
	}

	// 删除标签及其多语言信息
	err = dao.HajiNewsTag.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言信息
		_, err1 := tx.Model(dao.HajiNewsTagLanguages.Table()).Ctx(ctx).
			Where(dao.HajiNewsTagLanguages.Columns().TagId, req.Id).
			Delete()
		if err1 != nil {
			return err
		}

		// 删除主表记录
		_, err1 = tx.Model(dao.HajiNewsTag.Table()).Ctx(ctx).
			Where(dao.HajiNewsTag.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	return err
}
