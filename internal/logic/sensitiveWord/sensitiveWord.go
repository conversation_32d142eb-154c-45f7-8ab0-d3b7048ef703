package sensitiveWord

import (
	"context"
	"database/sql"
	"errors"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/user_account_svc"
	entity "gtcms/internal/model/entity/user_account_svc"
	"gtcms/internal/service"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
)

type sSensitiveWord struct{}

func init() {
	service.RegisterSensitiveWord(New())
}

func New() *sSensitiveWord {
	return &sSensitiveWord{}
}

// 敏感词分类添加
func (s *sSensitiveWord) CategoryAdd(ctx context.Context, req *v1.SensitiveWordCategoryAddReq) error {
	// 参数校验
	if "" == req.Word {
		return gerror.New("敏感词类型不能为空")
	}
	if 0 >= req.Sort {
		return gerror.New("排序不能为空")
	}
	// 判断是否存在
	total, err := dao.SensitiveCategory.Ctx(ctx).Where(dao.SensitiveCategory.Columns().Name, req.Word).Count()
	if err != nil {
		return err
	}
	if total > 0 {
		return gerror.New("此敏感词类型已存在")
	}
	currentTime := time.Now().UnixMilli()
	data := entity.SensitiveCategory{
		Name:       req.Word,
		Sort:       req.Sort,
		CreateTime: currentTime,
	}
	_, err = dao.SensitiveCategory.Ctx(ctx).Data(data).Insert()
	return err
}

// 敏感词分类详情
func (s *sSensitiveWord) CategoryInfo(ctx context.Context, req *v1.SensitiveWordCategoryInfoReq) (out *v1.SensitiveWordCategoryInfoRes, err error) {
	out = new(v1.SensitiveWordCategoryInfoRes)
	if req.Id <= 0 {
		return out, gerror.New("参数错误")
	}
	var data entity.SensitiveCategory
	err = dao.SensitiveCategory.Ctx(ctx).Where("id", req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	out.Id = gconv.Int(data.Id)
	out.Sort = data.Sort
	out.Word = data.Name
	return out, nil
}

// 敏感词分类列表
func (s *sSensitiveWord) CategoryList(ctx context.Context, req *v1.SensitiveWordCategoryListReq) (out *v1.SensitiveWordCategoryListRes, err error) {
	out = new(v1.SensitiveWordCategoryListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.SensitiveCategory.Ctx(ctx)
	if "" != req.Word {
		orm = orm.Where(dao.SensitiveCategory.Columns().Name+" like ?", "%"+req.Word+"%")
	}
	total, err := orm.Count()
	if err != nil {
		return
	}
	out.Total = total
	if total <= 0 {
		out.List = []v1.SensitiveWordCategoryInfoRes{}
		return
	}
	var list []entity.SensitiveCategory
	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.SensitiveCategory.Columns().Sort).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}
	for _, v := range list {
		out.List = append(out.List, v1.SensitiveWordCategoryInfoRes{
			Id:   gconv.Int(v.Id),
			Sort: v.Sort,
			Word: v.Name,
		})
	}

	return out, nil
}

// 敏感词分类编辑
func (s *sSensitiveWord) CategoryEdit(ctx context.Context, req *v1.SensitiveWordCategoryEditReq) error {
	// 参数验证
	if 0 >= req.Id {
		return gerror.New("敏感词分类ID不能为空")
	}
	if req.Word == "" {
		return gerror.New("敏感词分类不能为空")
	}
	if req.Sort < 0 || req.Sort > 9999 {
		return gerror.New("排序值错误")
	}
	// 判断是否存在
	var category entity.SensitiveCategory
	err := dao.SensitiveCategory.Ctx(ctx).Where(dao.SensitiveCategory.Columns().Id, req.Id).Scan(&category)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if 0 >= category.Id {
		return gerror.New("敏感词分类不存在")
	}
	currentTime := time.Now().UnixMilli()
	data := g.Map{
		dao.SensitiveCategory.Columns().Name:       req.Word,
		dao.SensitiveCategory.Columns().Sort:       req.Sort,
		dao.SensitiveCategory.Columns().UpdateTime: currentTime,
	}
	_, err = dao.SensitiveCategory.Ctx(ctx).
		Where(dao.SensitiveCategory.Columns().Id, req.Id).
		Data(data).
		Update()
	return err
}

// 敏感词分类删除
func (s *sSensitiveWord) CategoryDel(ctx context.Context, req *v1.SensitiveWordCategoryDelReq) error {
	_, err := dao.SensitiveCategory.Ctx(ctx).
		WhereIn(dao.SensitiveCategory.Columns().Id, req.Ids).
		Delete()
	return err
}

// 敏感词分类添加
func (s *sSensitiveWord) Add(ctx context.Context, req *v1.SensitiveWordAddReq) error {
	// 参数校验
	if 0 >= req.CategoryId {
		return gerror.New("分类id不能为空")
	}
	if "" == req.Word {
		return gerror.New("敏感词不能为空")
	}
	if 0 >= req.MatchType || 2 < req.MatchType {
		return gerror.New("匹配规则错误")
	}
	// 判断是否重复
	total, err := dao.Sensitive.Ctx(ctx).
		Where(dao.Sensitive.Columns().CategoryId, req.CategoryId).
		Where(dao.Sensitive.Columns().Word, req.Word).
		Count()
	if err != nil {
		return err
	}
	if total > 0 {
		return gerror.New("敏感词：" + req.Word + "已存在，不能重复添加")
	}
	currentTime := time.Now().UnixMilli()
	data := g.Map{
		dao.Sensitive.Columns().CategoryId: req.CategoryId,
		dao.Sensitive.Columns().MatchType:  req.MatchType,
		dao.Sensitive.Columns().Word:       req.Word,
		dao.Sensitive.Columns().CreateTime: currentTime,
		dao.Sensitive.Columns().Status:     req.Status,
	}
	_, err = dao.Sensitive.Ctx(ctx).Data(data).Insert()
	return err
}

// 敏感词分类详情
func (s *sSensitiveWord) Info(ctx context.Context, req *v1.SensitiveWordInfoReq) (out *v1.SensitiveWordInfoRes, err error) {
	out = new(v1.SensitiveWordInfoRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var data entity.Sensitive
	err = dao.Sensitive.Ctx(ctx).Where(dao.Sensitive.Columns().Id, req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	out = &v1.SensitiveWordInfoRes{
		Id:         gconv.Int(data.Id),
		CategoryId: data.CategoryId,
		MatchType:  data.MatchType,
		Status:     data.Status,
		Word:       data.Word,
	}
	return out, err
}

// 敏感词分类编辑
func (s *sSensitiveWord) Edit(ctx context.Context, req *v1.SensitiveWordEditReq) error {
	if 0 >= req.Id {
		return gerror.New("参数错误")
	}
	// 判断是否存在
	var info entity.Sensitive
	err := dao.Sensitive.Ctx(ctx).Where(dao.Sensitive.Columns().Id, req.Id).Scan(&info)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if info.Id == 0 {
		return gerror.New("敏感词不存在")
	}
	// 判断是否重复
	total, err := dao.Sensitive.Ctx(ctx).
		Where(dao.Sensitive.Columns().Word, req.Word).
		WhereNot(dao.Sensitive.Columns().Id, req.Id).
		Where(dao.Sensitive.Columns().CategoryId, req.CategoryId).
		Count()
	if err != nil {
		return err
	}
	if total > 0 {
		return gerror.New("敏感词已存在，不能重复添加")
	}
	data := g.Map{
		dao.Sensitive.Columns().CategoryId: req.CategoryId,
		dao.Sensitive.Columns().MatchType:  req.MatchType,
		dao.Sensitive.Columns().Status:     req.Status,
		dao.Sensitive.Columns().Word:       req.Word,
		dao.Sensitive.Columns().UpdateTime: time.Now().UnixMilli(),
	}
	_, err = dao.Sensitive.Ctx(ctx).Where(dao.Sensitive.Columns().Id, req.Id).Data(data).Update()
	return err
}

// 敏感词分类列表
func (s *sSensitiveWord) List(ctx context.Context, req *v1.SensitiveWordListReq) (out *v1.SensitiveWordListRes, err error) {
	out = new(v1.SensitiveWordListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.Sensitive.Ctx(ctx)
	if req.CategoryId > 0 {
		orm = orm.Where(dao.Sensitive.Columns().CategoryId, req.CategoryId)
	}
	if -1 != req.Status {
		orm = orm.Where(dao.Sensitive.Columns().Status, req.Status)
	}
	if req.Word != "" {
		orm = orm.WhereLike(dao.Sensitive.Columns().Word, "%"+req.Word+"%")
	}
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.SensitiveWordListItem{}
		return out, err
	}
	if out.Total == 0 {
		out.List = []v1.SensitiveWordListItem{}
		return out, err
	}
	var list []entity.Sensitive
	// 分页查询
	err = orm.Page(req.Current, req.PageSize).OrderDesc(dao.Sensitive.Columns().CreateTime).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.SensitiveWordListItem{}
		return out, err
	}
	// 提取分类id
	categoryIds := gutil.ListItemValuesUnique(list, "CategoryId")
	var categoryList []entity.SensitiveCategory
	err = dao.SensitiveCategory.Ctx(ctx).WhereIn(dao.SensitiveCategory.Columns().Id, categoryIds).Scan(&categoryList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.SensitiveWordListItem{}
		return out, err
	}
	var categoryMap = make(map[int]string)
	for _, v := range categoryList {
		categoryMap[gconv.Int(v.Id)] = v.Name
	}
	// 计算序号开始值
	serialNumber := (req.Current-1)*req.PageSize + 1
	for _, v := range list {
		// 时间戳转字符串
		createTime := time.Unix(gconv.Int64(v.CreateTime)/1000, 0).Format("2006-01-02 15:04:05")
		statusText := ""
		if consts.Zero == v.Status {
			statusText = "停用"
		} else if consts.One == v.Status {
			statusText = "启用"
		}
		out.List = append(out.List, v1.SensitiveWordListItem{
			CategoryId:    v.CategoryId,
			CategoryName:  categoryMap[v.CategoryId],
			CreateTime:    createTime,
			Id:            gconv.Int(v.Id),
			MatchType:     v.MatchType,
			MatchTypeText: consts.SensitiveWordMatchTypeName(v.MatchType),
			SerialNumber:  serialNumber,
			Status:        v.Status,
			StatusText:    statusText,
		})
		serialNumber++
	}
	return out, err
}

// 删除敏感词
func (s *sSensitiveWord) Delete(ctx context.Context, req *v1.SensitiveWordDelReq) error {
	_, err := dao.Sensitive.Ctx(ctx).WhereIn(dao.Sensitive.Columns().Id, req.Ids).Delete()
	return err
}
