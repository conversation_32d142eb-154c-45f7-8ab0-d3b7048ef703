package hajiLandmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/shopspring/decimal"
)

type sHajiLandmark struct{}

func init() {
	service.RegisterHajiLandmark(New())
}

func New() *sHajiLandmark {
	return &sHajiLandmark{}
}

// ==================== 地标类型相关接口实现 ====================

func (s *sHajiLandmark) TypeList(ctx context.Context, req *v1.HajiLandmarkTypeListReq) (out *v1.HajiLandmarkTypeListRes, err error) {
	out = new(v1.HajiLandmarkTypeListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var types []entity.HajiLandmarkType
	query := dao.HajiLandmarkType.Ctx(ctx).OrderAsc(dao.HajiLandmarkType.Columns().CreateTime)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&types)
	if err != nil {
		return nil, err
	}

	// 总数
	out.Total, err = dao.HajiLandmarkType.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 提取id
	typeIds := gutil.ListItemValuesUnique(types, "Id")

	var typeLanguages []entity.HajiLandmarkTypeLanguages
	if len(typeIds) > 0 {
		err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			WhereIn(dao.HajiLandmarkTypeLanguages.Columns().TypeId, typeIds).
			Scan(&typeLanguages)
		if err != nil {
			return nil, err
		}
	}

	typeLanguageMap := make(map[uint64][]entity.HajiLandmarkTypeLanguages)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = append(typeLanguageMap[lang.TypeId], lang)
	}

	// 获取每个类型的使用次数
	typeUseCounts, err := s.getTypeUseCounts(ctx, typeIds)
	if err != nil {
		return nil, err
	}

	for _, typeItem := range types {
		item := &v1.HajiLandmarkTypeListItem{
			Id:          int(typeItem.Id),
			IconType:    typeItem.IconType,
			UseCount:    typeUseCounts[typeItem.Id],
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		for _, lang := range typeLanguageMap[typeItem.Id] {
			if int(lang.LanguageId) == languageId {
				item.TypeName = lang.TypeName
				break
			}
		}

		s.buildTypeLanguageArr(item, typeLanguageMap[typeItem.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// getTypeUseCounts 获取地标类型的使用次数
func (s *sHajiLandmark) getTypeUseCounts(ctx context.Context, typeIds []any) (map[uint64]int, error) {
	typeUseCounts := make(map[uint64]int)

	if len(typeIds) == 0 {
		return typeUseCounts, nil
	}

	// 使用一条 GROUP BY 查询获取所有类型的使用次数
	var useCounts []struct {
		TypeId uint64 `json:"type_id"`
		Count  int    `json:"count"`
	}

	err := dao.HajiLandmark.Ctx(ctx).
		Fields("type_id, COUNT(*) as count").
		WhereIn(dao.HajiLandmark.Columns().TypeId, typeIds).
		Group(dao.HajiLandmark.Columns().TypeId).
		Scan(&useCounts)
	if err != nil {
		return nil, err
	}

	// 构建 map
	for _, item := range useCounts {
		typeUseCounts[item.TypeId] = item.Count
	}

	return typeUseCounts, nil
}

// buildTypeLanguageArr 构建地标类型语言支持列表
func (s *sHajiLandmark) buildTypeLanguageArr(item *v1.HajiLandmarkTypeListItem, languages []entity.HajiLandmarkTypeLanguages) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, lang := range languages {
		languageSupport[int(lang.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sHajiLandmark) TypeAdd(ctx context.Context, req *v1.HajiLandmarkTypeCreateReq) (out *v1.HajiLandmarkTypeCreateRes, err error) {
	out = new(v1.HajiLandmarkTypeCreateRes)

	// 检查类型名称是否重复
	// 这个循环一般最多3次，所以性能问题不大，这样写方便一些
	for _, item := range req.Content {
		count, err := dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	var typeId int64
	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		typeId, err1 = tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmarkType{IconType: req.IconType}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.HajiLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkTypeLanguages{
				TypeId:     typeId,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = int(typeId)
	return out, nil
}

func (s *sHajiLandmark) TypeEdit(ctx context.Context, req *v1.HajiLandmarkTypeEditReq) (out *v1.HajiLandmarkTypeEditRes, err error) {
	out = new(v1.HajiLandmarkTypeEditRes)

	// 检查类型是否存在
	var typeItem entity.HajiLandmarkType
	err = dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查类型名称是否重复（排除自己）
	for _, item := range req.Content {
		count, err := dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			WhereNot(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmarkType{
				IconType: req.IconType,
			}).
			Where(dao.HajiLandmarkType.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.HajiLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkTypeLanguages{
				TypeId:     req.Id,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sHajiLandmark) TypeOne(ctx context.Context, req *v1.HajiLandmarkTypeOneReq) (out *v1.HajiLandmarkTypeOneRes, err error) {
	out = new(v1.HajiLandmarkTypeOneRes)

	var typeItem entity.HajiLandmarkType
	err = dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "地标类型不存在")
	}

	// 获取多语言内容
	var languages []entity.HajiLandmarkTypeLanguages
	err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
		Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	out.Id = int(typeItem.Id)
	out.IconType = typeItem.IconType
	out.Content = make([]v1.HajiLandmarkTypeEditItem, 0)

	for _, lang := range languages {
		out.Content = append(out.Content, v1.HajiLandmarkTypeEditItem{
			LanguageType: int(lang.LanguageId),
			TypeName:     lang.TypeName,
		})
	}

	return out, nil
}

func (s *sHajiLandmark) TypeDelete(ctx context.Context, req *v1.HajiLandmarkTypeDeleteReq) (out *v1.HajiLandmarkTypeDeleteRes, err error) {
	out = new(v1.HajiLandmarkTypeDeleteRes)

	// 检查类型是否存在
	count, err := dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查该类型下是否有关联的地标
	landmarkCount, err := dao.HajiLandmark.Ctx(ctx).
		Where(dao.HajiLandmark.Columns().TypeId, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if landmarkCount > 0 {
		return nil, gerror.New("该地点类型下有关联内容，不允许删除！")
	}

	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除type表记录
		_, err1 = tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkType.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

// ==================== 地标相关接口实现 ====================

func (s *sHajiLandmark) List(ctx context.Context, req *v1.HajiLandmarkListReq) (out *v1.HajiLandmarkListRes, err error) {
	out = new(v1.HajiLandmarkListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var landmarks []entity.HajiLandmark
	query := dao.HajiLandmark.Ctx(ctx).OrderAsc(dao.HajiLandmark.Columns().Id)

	// 按类型筛选
	innerType := req.InnerType
	if len(req.InnerType) == 0 {
		innerType = "destinasi"
	}
	query = query.Where(dao.HajiLandmark.Columns().InnerType, innerType)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&landmarks)
	if err != nil {
		return nil, err
	}

	// 总数
	countQuery := dao.HajiLandmark.Ctx(ctx).Where(dao.HajiLandmark.Columns().InnerType, innerType)
	out.Total, err = countQuery.Count()
	if err != nil {
		return nil, err
	}

	if len(landmarks) == 0 {
		return out, nil
	}

	// 先提取id
	landmarkIds := gutil.ListItemValuesUnique(landmarks, "Id")
	typeIds := gutil.ListItemValuesUnique(landmarks, "TypeId")

	// 获取地标多语言内容
	var landmarkLanguages []entity.HajiLandmarkLanguages
	err = dao.HajiLandmarkLanguages.Ctx(ctx).
		WhereIn(dao.HajiLandmarkLanguages.Columns().LandmarkId, landmarkIds).
		Scan(&landmarkLanguages)
	if err != nil {
		return nil, err
	}

	// 获取地标类型多语言内容
	var typeLanguages []entity.HajiLandmarkTypeLanguages
	if len(typeIds) > 0 {
		err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			WhereIn(dao.HajiLandmarkTypeLanguages.Columns().TypeId, typeIds).
			Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, languageId).
			Scan(&typeLanguages)
		if err != nil {
			return nil, err
		}
	}

	landmarkLanguageMap := make(map[uint64][]entity.HajiLandmarkLanguages)
	for _, lang := range landmarkLanguages {
		landmarkLanguageMap[lang.LandmarkId] = append(landmarkLanguageMap[lang.LandmarkId], lang)
	}

	typeLanguageMap := make(map[uint64]string)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = lang.TypeName
	}

	// 返回
	for _, landmark := range landmarks {
		item := &v1.HajiLandmarkListItem{
			Id:          int(landmark.Id),
			TypeId:      int(landmark.TypeId),
			TypeName:    typeLanguageMap[landmark.TypeId],
			Latitude:    landmark.Latitude.String(),
			Longitude:   landmark.Longitude.String(),
			ImageUrl:    landmark.ImageUrl,
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		// 设置当前语言的内容
		for _, lang := range landmarkLanguageMap[landmark.Id] {
			if int(lang.LanguageId) == languageId {
				item.LandmarkName = lang.LandmarkName
				item.ShortDescription = lang.ShortDescription
				item.Address = lang.Address
				break
			}
		}

		// 多语言
		s.buildLandmarkLanguageArr(item, landmarkLanguageMap[landmark.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// buildLandmarkLanguageArr 构建地标语言支持列表
func (s *sHajiLandmark) buildLandmarkLanguageArr(item *v1.HajiLandmarkListItem, languages []entity.HajiLandmarkLanguages) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, lang := range languages {
		languageSupport[int(lang.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sHajiLandmark) Add(ctx context.Context, req *v1.HajiLandmarkCreateReq) (out *v1.HajiLandmarkCreateRes, err error) {
	out = new(v1.HajiLandmarkCreateRes)

	// 检查地标类型是否存在
	typeCount, err := dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.TypeId).
		Count()
	if err != nil {
		return nil, err
	}
	if typeCount == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 转换坐标
	latitude, err := decimal.NewFromString(req.Latitude)
	if err != nil {
		return nil, gerror.New("纬度格式不正确")
	}
	longitude, err := decimal.NewFromString(req.Longitude)
	if err != nil {
		return nil, gerror.New("经度格式不正确")
	}

	var landmarkId int64
	err = dao.HajiLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		landmarkId, err1 = tx.Model(dao.HajiLandmark.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmark{
				TypeId:    req.TypeId,
				InnerType: req.InnerType,
				Latitude:  latitude,
				Longitude: longitude,
				ImageUrl:  req.ImageUrl,
				SortOrder: 0, // 产品没有提出需要这个字段，先留空吧
			}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.HajiLandmarkLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkLanguages{
				LandmarkId:       landmarkId,
				LanguageId:       item.LanguageType,
				LandmarkName:     item.LandmarkName,
				Country:          item.Country, // 后续可以通过Google Map API获取
				Address:          item.Address,
				ShortDescription: item.ShortDescription,
				InformationText:  item.InformationText,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = int(landmarkId)
	return out, nil
}

func (s *sHajiLandmark) Edit(ctx context.Context, req *v1.HajiLandmarkEditReq) (out *v1.HajiLandmarkEditRes, err error) {
	out = new(v1.HajiLandmarkEditRes)

	// 检查地标是否存在
	var landmark entity.HajiLandmark
	err = dao.HajiLandmark.Ctx(ctx).
		Where(dao.HajiLandmark.Columns().Id, req.Id).
		Scan(&landmark)
	if err != nil {
		return nil, err
	}
	if landmark.Id == 0 {
		return nil, gerror.New("地标不存在")
	}

	// 检查地标类型是否存在
	typeCount, err := dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.TypeId).
		Count()
	if err != nil {
		return nil, err
	}
	if typeCount == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 转换坐标
	latitude, err := decimal.NewFromString(req.Latitude)
	if err != nil {
		return nil, gerror.New("纬度格式不正确")
	}
	longitude, err := decimal.NewFromString(req.Longitude)
	if err != nil {
		return nil, gerror.New("经度格式不正确")
	}

	err = dao.HajiLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.HajiLandmark.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmark{
				TypeId:    req.TypeId,
				Latitude:  latitude,
				Longitude: longitude,
				ImageUrl:  req.ImageUrl,
				SortOrder: 0,
			}).
			Where(dao.HajiLandmark.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.HajiLandmarkLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkLanguages.Columns().LandmarkId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.HajiLandmarkLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkLanguages{
				LandmarkId:       req.Id,
				LanguageId:       item.LanguageType,
				LandmarkName:     item.LandmarkName,
				Country:          item.Country,
				Address:          item.Address,
				ShortDescription: item.ShortDescription,
				InformationText:  item.InformationText,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sHajiLandmark) One(ctx context.Context, req *v1.HajiLandmarkOneReq) (res *v1.HajiLandmarkOneRes, err error) {
	var landmark entity.HajiLandmark
	err = dao.HajiLandmark.Ctx(ctx).
		Where(dao.HajiLandmark.Columns().Id, req.Id).
		Scan(&landmark)
	if err != nil {
		return nil, err
	}
	if landmark.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "地标不存在")
	}

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	// 获取多语言内容
	var languages []entity.HajiLandmarkLanguages
	err = dao.HajiLandmarkLanguages.Ctx(ctx).
		Where(dao.HajiLandmarkLanguages.Columns().LandmarkId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 获取地标类型名称
	var typeLanguage entity.HajiLandmarkTypeLanguages
	err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
		Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, landmark.TypeId).
		Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, languageId).
		Scan(&typeLanguage)
	if err != nil {
		return nil, err
	}

	// 构建返回数据
	item := &v1.HajiLandmarkDetail{
		Id:        int(landmark.Id),
		TypeId:    int(landmark.TypeId),
		InnerType: landmark.InnerType,
		Latitude:  landmark.Latitude.String(),
		Longitude: landmark.Longitude.String(),
		ImageUrl:  landmark.ImageUrl,
		Content:   make([]v1.HajiLandmarkItem, 0, len(languages)),
	}

	// 设置当前语言的内容
	for _, lang := range languages {
		item.Content = append(item.Content, v1.HajiLandmarkItem{
			LanguageType:     int(lang.LanguageId),
			LandmarkName:     lang.LandmarkName,
			Country:          lang.Country,
			ShortDescription: lang.ShortDescription,
			Address:          lang.Address,
			InformationText:  lang.InformationText,
		})
	}

	res = &v1.HajiLandmarkOneRes{
		HajiLandmarkDetail: *item,
	}
	return res, nil
}

func (s *sHajiLandmark) Delete(ctx context.Context, req *v1.HajiLandmarkDeleteReq) (out *v1.HajiLandmarkDeleteRes, err error) {
	out = new(v1.HajiLandmarkDeleteRes)

	// 检查地标是否存在
	count, err := dao.HajiLandmark.Ctx(ctx).
		Where(dao.HajiLandmark.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("地标不存在")
	}

	err = dao.HajiLandmark.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.HajiLandmarkLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkLanguages.Columns().LandmarkId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除主表记录
		_, err1 = tx.Model(dao.HajiLandmark.Table()).Ctx(ctx).
			Where(dao.HajiLandmark.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}
