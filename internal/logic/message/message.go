package message

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/notify_svc"
	userAccountDao "gtcms/internal/dao/user_account_svc"
	do "gtcms/internal/model/do/notify_svc"
	model "gtcms/internal/model/notify"
	"gtcms/internal/service"
)

type sMessage struct{}

func init() {
	service.RegisterMessage(New())
}
func New() *sMessage {
	return &sMessage{}
}

func (f *sMessage) Add(ctx context.Context, req *v1.MessageCreateReq) (res *v1.MessageCreateRes, err error) {
	err = dao.Message.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var count int
		if req.MessageMode == consts.One {
			count, err = userAccountDao.User.Ctx(ctx).Where("id > ?", 0).Count()
			if err != nil {
				return err
			}
		} else {
			count = len(req.UserIds)
		}

		lastId, err := tx.Model(dao.Message.Table()).Data(do.Message{
			MessageMode: req.MessageMode,
			SendNums:    count,
			//CronTime:    req.CronTime,
			Content: make([]byte, 0),
		}).InsertAndGetId()
		if err != nil {
			return err
		}
		var messageLanguages = make([]do.MessageLanguage, 0, len(req.Item))
		for _, item := range req.Item {
			messageLanguages = append(messageLanguages, do.MessageLanguage{
				MessageId:  lastId,
				LanguageId: item.LanguageId,
				Title:      item.Title,
				Content:    item.Content,
			})
		}
		_, err = tx.Model(dao.MessageLanguage.Table()).Insert(messageLanguages)
		if err != nil {
			return err
		}
		var messageUsers = make([]do.MessageUser, 0, len(req.UserIds))
		for _, userId := range req.UserIds {
			messageUsers = append(messageUsers, do.MessageUser{
				MessageId: lastId,
				UserId:    userId,
			})
		}
		_, err = tx.Model(dao.MessageUser.Table()).Insert(messageUsers)

		return err
	})
	if err != nil {
		return
	}
	return
}

func (f *sMessage) MessageEdit(ctx context.Context, req *v1.MessageEditReq) (res *v1.MessageEditRes, err error) {
	return
}

func (f *sMessage) MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error) {
	return
}

func (f *sMessage) MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageOneRes, err error) {
	var item model.MessageWithLanguage
	err = dao.Message.Ctx(ctx).Where(dao.Message.Columns().Id, req.Id).
		With(model.MessageWithLanguage{}.Language, model.MessageWithLanguage{}.User).Scan(&item)
	if err != nil {
		return
	}

	return &v1.MessageOneRes{
		MessageWithLanguage: item,
	}, nil
}

func (f *sMessage) MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListRes, err error) {

	var list []*model.MessageWithLanguage
	md := dao.Message.Ctx(ctx)
	if req.Title != "" {
		sub := dao.MessageLanguage.Ctx(ctx).
			Fields("message_language.message_id").
			Where("message_language.message_id = message.id").
			WhereLike("message_language.title", "%"+req.Title+"%")
		md = md.Where("EXISTS (?)", sub)
	}
	if req.StartTime > 0 {
		md = md.Where("message.create_time >= ?", req.StartTime).Where("message.create_time <= ?", req.EndTime)
	}

	var total int
	err = md.OrderDesc("id").With(model.MessageWithLanguage{}.Language, model.MessageWithLanguage{}.User).
		Page(req.ListReq.Current, req.ListReq.PageSize).ScanAndCount(&list, &total, false)
	if err != nil {
		return
	}

	return &v1.MessageListRes{
		List: list,
		ListRes: v1.ListRes{
			Total:   total,
			Current: req.Current,
		},
	}, nil
}
