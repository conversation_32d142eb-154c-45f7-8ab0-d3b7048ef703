package user

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	islanmic_dao "gtcms/internal/dao/islamic_content_svc"
	dao "gtcms/internal/dao/user_account_svc"
	islanmic_entity "gtcms/internal/model/entity/islamic_content_svc"
	entity "gtcms/internal/model/entity/user_account_svc"
	"gtcms/internal/service"
)

type (
	sUser struct{}
)

func init() {
	service.RegisterUser(New())
}

var cl = dao.User.Columns()

func delCache(id uint) gdb.CacheOption {
	return gdb.CacheOption{
		Duration: -1,
		Name:     consts.KeyAccountInfo(id),
		Force:    false,
	}
}

func New() service.IUser {
	return &sUser{}
}

func (s *sUser) List(ctx context.Context, in *v1.UserListReq) (res *v1.UserListRes, err error) {
	out := new(v1.UserListRes)
	out.Current = in.Current
	md := dao.User.Ctx(ctx) //.Handler(utility.AccountFilter)
	if in.CreateTimeStart > 0 {
		md = md.WhereGTE(cl.CreateTime, in.CreateTimeStart)
	}
	if in.CreateTimeEnd > 0 {
		md = md.WhereLTE(cl.CreateTime, in.CreateTimeEnd)
	}
	if in.Id > 0 {
		md = md.Where(cl.Id, in.Id)
	}
	if in.IsBanned > 0 {
		md = md.Where(cl.IsBanned, in.IsBanned)
	}
	if in.PhoneNum != "" {
		md = md.Where(cl.PhoneNum, in.PhoneNum)
	}
	//查询用户信息表-start
	var infoList []*entity.UserInfo
	userInfo := dao.UserInfo.Ctx(ctx) //.Handler(utility.AccountFilter)
	if in.FirstName != "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().FirstName, in.FirstName)
	}
	if in.Email != "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().Email, in.Email)
	}
	if in.Gender != "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().Gender, in.Gender)
	}
	if in.Nickname != "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().Nickname, in.Nickname)
	}
	if in.LastSigninIp != "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().LastSigninIp, in.LastSigninIp)
	}
	//没有查询条件时 不查询
	if in.FirstName == "" && in.Email == "" && in.Gender == "" && in.Nickname == "" && in.LastSigninIp == "" {
		userInfo = userInfo.Where(dao.UserInfo.Columns().Id, 0)
	}
	err = userInfo.Scan(&infoList)
	var ids []uint64
	//init map
	infoMap := make(map[uint64]*entity.UserInfo, len(infoList))
	if len(infoList) > 0 {
		for _, v := range infoList {
			ids = append(ids, v.Id)
			infoMap[v.UserId] = v
		}
		md = md.WhereIn(cl.Id, ids)
	}
	//查询用户信息表-end

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&out.List)

	//查询NU 认证表--start
	var nuIds []uint64
	for _, v := range out.List {
		nuIds = append(nuIds, v.Id)
	}
	var nuInfoList []*entity.UserNuId

	nuInfo := dao.UserNuId.Ctx(ctx).WhereIn(dao.UserNuId.Columns().UserId, nuIds)
	err = nuInfo.Scan(&nuInfoList)
	nuInfoMap := make(map[uint64]*entity.UserNuId, len(nuInfoList))
	if len(nuInfoList) > 0 {
		for _, v := range nuInfoList {
			if v.Status == 2 { // 只查询已认证的NU
				nuInfoMap[v.UserId] = v
			}
		}
	}
	//查询NU 认证表-end
	for _, v := range out.List {
		if info, ok := infoMap[v.Id]; ok {
			v.FirstName = info.FirstName
			v.MiddleName = info.MiddleName
			v.LastName = info.LastName
			v.Email = info.Email
			v.CountryId = info.CountryId
			v.Avatar = info.Avatar
			v.Gender = info.Gender
			v.Nickname = info.Nickname
			v.Address = info.Address
			v.DataType = info.DataType
			v.Source = info.Source
			v.LastSigninTime = info.LastSigninTime
			v.LastSigninIp = info.LastSigninIp
		}
		if _, ok := nuInfoMap[v.Id]; ok {
			v.IsNu = 1
		}
	}
	return out, err
}

func (s *sUser) Info(ctx context.Context, in *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {

	out := new(v1.UserInfoRes)

	var user *entity.User
	dao.User.Ctx(ctx).Where(cl.Id, in.Id).Scan(&user) //.Handler(utility.AccountFilter)
	if user == nil {
		return res, gerror.Newf("User with ID %d does not exist", in.Id)
	}
	gconv.Struct(user, &out)
	var userInfo *entity.UserInfo
	dao.UserInfo.Ctx(ctx).Where(dao.UserInfo.Columns().UserId, in.Id).Scan(&userInfo)

	if userInfo != nil && userInfo.Id > 0 {
		out.FirstName = userInfo.FirstName
		out.MiddleName = userInfo.MiddleName
		out.LastName = userInfo.LastName
		out.Email = userInfo.Email
		out.CountryId = userInfo.CountryId
		out.Avatar = userInfo.Avatar
		out.Gender = userInfo.Gender
		out.Nickname = userInfo.Nickname
		out.Address = userInfo.Address
		out.LastSigninTime = userInfo.LastSigninTime
		out.YearOfBirth = userInfo.YearOfBirth
		out.MonthOfBirth = userInfo.MonthOfBirth
		out.DayOfBirth = userInfo.DayOfBirth
		out.LastSigninIp = userInfo.LastSigninIp
	}

	//查询NU 认证表-end
	var nuInfo *entity.UserNuId
	dao.UserNuId.Ctx(ctx).Where(dao.UserNuId.Columns().UserId, in.Id).Scan(&nuInfo)
	if nuInfo != nil && nuInfo.Id > 0 {
		if nuInfo.Status == 2 { // 只查询已认证的NU
			out.IsNu = 1
		}
	}

	//查询address
	var addressInfo []*entity.UserAddress
	AddressInfo := make([]*v1.AddressInfo, 0)
	dao.UserAddress.Ctx(ctx).Where(dao.UserAddress.Columns().UserId, in.Id).Scan(&addressInfo)
	if addressInfo != nil && len(addressInfo) > 0 {

		for _, v := range addressInfo {
			address := &v1.AddressInfo{
				Level1Name: v.Level1Name,
				Level2Name: v.Level2Name,
				Level3Name: v.Level3Name,
				Level4Name: v.Level4Name,
				Level1Code: v.Level1Code,
				Level2Code: v.Level2Code,
				Level3Code: v.Level3Code,
				Level4Code: v.Level4Code,

				Receiver:  v.Receiver,
				PhoneNum:  v.PhoneNum,
				Address:   v.Address,
				IsDefault: v.IsDefault,
			}
			AddressInfo = append(AddressInfo, address)
		}
	}
	out.AddressInfo = AddressInfo

	surahCollects, _ := islanmic_dao.SurahReadCollect.Ctx(ctx).Where(islanmic_dao.SurahReadCollect.Columns().UserId, in.Id).Count()
	surahViews, _ := islanmic_dao.SurahReadRecord.Ctx(ctx).Where(islanmic_dao.SurahReadRecord.Columns().UserId, in.Id).Count()
	newsShare, _ := islanmic_dao.NewsArticleShare.Ctx(ctx).Where(islanmic_dao.NewsArticleShare.Columns().UserId, in.Id).Count()
	loginCounts, _ := dao.UserSigninLog.Ctx(ctx).Where(dao.UserSigninLog.Columns().UserId, in.Id).Count()

	out.CollectCounts = surahCollects
	out.ViewCounts = surahViews
	out.ShareCounts = newsShare
	out.LoginCounts = loginCounts
	return out, err
}

func (s *sUser) BatchBan(ctx context.Context, in *v1.BatchBanReq) (res *v1.EmptyDataRes, err error) {
	_, err = dao.User.Ctx(ctx).WhereIn(cl.Id, in.Ids).Update(g.Map{
		cl.IsBanned: in.Status,
	})
	return
}
func (s *sUser) UserLoginLog(ctx context.Context, in *v1.UserLoginLogReq) (res *v1.UserLoginLogRes, err error) {
	out := new(v1.UserLoginLogRes)
	out.Current = in.Current
	md := dao.UserSigninLog.Ctx(ctx).Where(dao.UserSigninLog.Columns().UserId, in.Id)
	if in.TimeStart > 0 {
		md = md.WhereGTE(dao.UserSigninLog.Columns().SigninTime, in.TimeStart)
	}
	if in.TimeEnd > 0 {
		md = md.WhereLTE(dao.UserSigninLog.Columns().SigninTime, in.TimeEnd)
	}
	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).Order("signin_time desc").Scan(&out.List)
	return out, err
}

func (s *sUser) CollectList(ctx context.Context, in *v1.CollectListReq) (res *v1.RecordListRes, err error) {
	out := new(v1.RecordListRes)
	out.Current = in.Current
	switch in.Type { //activity-活动，article-文章，quran-古兰经，doa-祷告
	case "activity":

	case "article":
		var articleCollects []*islanmic_entity.NewsArticleCollect
		md := islanmic_dao.NewsArticleCollect.Ctx(ctx).Where(islanmic_dao.NewsArticleCollect.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.NewsArticleCollect.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.NewsArticleCollect.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&articleCollects)
		if len(articleCollects) > 0 {
			for _, v := range articleCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      v.ArticleName,
						Type:       in.Type,
					})
				}
			}
		}
	case "quran":
		var surahCollects []*islanmic_entity.SurahReadCollect
		md := islanmic_dao.SurahReadCollect.Ctx(ctx).Where(islanmic_dao.SurahReadCollect.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.SurahReadCollect.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.SurahReadCollect.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&surahCollects)
		if len(surahCollects) > 0 {
			for _, v := range surahCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      "QS. " + v.SurahName + ": Ayat " + gconv.String(v.AyahId),
						Type:       in.Type,
					})
				}
			}
		}
	case "doa":
		var doaCollects []*islanmic_entity.DoaReadCollect
		md := islanmic_dao.DoaReadCollect.Ctx(ctx).Where(islanmic_dao.DoaReadCollect.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.DoaReadCollect.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.DoaReadCollect.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&doaCollects)
		if len(doaCollects) > 0 {
			for _, v := range doaCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      v.BaccanName,
						Type:       in.Type,
					})
				}
			}
		}
	}

	return out, err
}

func (s *sUser) ReadList(ctx context.Context, in *v1.ReadListReq) (res *v1.RecordListRes, err error) {
	out := new(v1.RecordListRes)
	out.Current = in.Current
	switch in.Type { //activity-活动，article-文章，quran-古兰经，doa-祷告
	case "activity":

	case "article":
		var articleCollects []*islanmic_entity.NewsArticleView
		md := islanmic_dao.NewsArticleView.Ctx(ctx).Where(islanmic_dao.NewsArticleView.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.NewsArticleView.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.NewsArticleView.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&articleCollects)
		if len(articleCollects) > 0 {
			for _, v := range articleCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      v.ArticleName,
						Type:       in.Type,
					})
				}
			}
		}
	case "quran":
		var surahCollects []*islanmic_entity.SurahReadRecord
		md := islanmic_dao.SurahReadRecord.Ctx(ctx).Where(islanmic_dao.SurahReadRecord.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.SurahReadRecord.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.SurahReadRecord.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&surahCollects)
		if len(surahCollects) > 0 {
			for _, v := range surahCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      "QS. " + v.SurahName + ": Ayat " + gconv.String(v.AyahId),
						Type:       in.Type,
					})
				}
			}
		}
	case "doa":
		var doaCollects []*islanmic_entity.DoaRead
		md := islanmic_dao.DoaRead.Ctx(ctx).Where(islanmic_dao.DoaRead.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.DoaRead.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.DoaRead.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&doaCollects)
		if len(doaCollects) > 0 {
			for _, v := range doaCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      v.BaccanName,
						Type:       in.Type,
					})
				}
			}
		}
	}

	return out, err
}

func (s *sUser) ShareList(ctx context.Context, in *v1.ShareListReq) (res *v1.RecordListRes, err error) {
	out := new(v1.RecordListRes)
	out.Current = in.Current
	switch in.Type { //activity-活动，article-文章，quran-古兰经，doa-祷告
	case "activity":

	case "article":
		var articleCollects []*islanmic_entity.NewsArticleShare
		md := islanmic_dao.NewsArticleShare.Ctx(ctx).Where(islanmic_dao.NewsArticleShare.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.NewsArticleShare.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.NewsArticleShare.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&articleCollects)
		if len(articleCollects) > 0 {
			for _, v := range articleCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      v.ArticleName,
						Type:       in.Type,
					})
				}
			}
		}
	case "quran":
		var surahCollects []*islanmic_entity.SurahShare
		md := islanmic_dao.SurahShare.Ctx(ctx).Where(islanmic_dao.SurahShare.Columns().UserId, in.Id)
		if in.CreateTimeStart > 0 {
			md = md.WhereGTE(islanmic_dao.SurahShare.Columns().CreateTime, in.CreateTimeStart)
		}
		if in.CreateTimeEnd > 0 {
			md = md.WhereLTE(islanmic_dao.SurahShare.Columns().CreateTime, in.CreateTimeEnd)
		}
		out.Total, err = md.Count()
		if err != nil {
			return nil, err
		}
		err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&surahCollects)
		if len(surahCollects) > 0 {
			for _, v := range surahCollects {
				if v != nil {
					out.List = append(out.List, &v1.RecordListVo{
						Id:         uint64(v.Id),
						CreateTime: v.CreateTime,
						Title:      "QS. " + v.SurahName + ": Ayat " + gconv.String(v.AyahId),
						Type:       in.Type,
					})
				}
			}
		}
	case "doa": //暂无功能
		//var doaCollects []*islanmic_entity.DoaReadCollect
		//md := islanmic_dao.DoaReadCollect.Ctx(ctx).Where(islanmic_dao.DoaReadCollect.Columns().UserId, in.Id)
		//if in.CreateTimeStart > 0 {
		//	md = md.WhereGTE(islanmic_dao.DoaReadCollect.Columns().CreateTime, in.CreateTimeStart)
		//}
		//if in.CreateTimeEnd > 0 {
		//	md = md.WhereLTE(islanmic_dao.DoaReadCollect.Columns().CreateTime, in.CreateTimeEnd)
		//}
		//out.Total, err = md.Count()
		//if err != nil {
		//	return nil, err
		//}
		//err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&doaCollects)
		//if len(doaCollects) > 0 {
		//	for _, v := range doaCollects {
		//		if v != nil {
		//			out.List = append(out.List, &v1.RecordListVo{
		//				Id:         uint64(v.Id),
		//				CreateTime: v.CreateTime,
		//				Title:      v.BaccanName,
		//				Type:       in.Type,
		//			})
		//		}
		//	}
		//}
	}

	return out, err
}
