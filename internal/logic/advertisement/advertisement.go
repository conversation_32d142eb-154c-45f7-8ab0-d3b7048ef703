package advertisement

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sAdvertisement struct{}

func init() {
	service.RegisterAdvertisement(New())
}

func New() *sAdvertisement {
	return &sAdvertisement{}
}

// PositionList 广告位置列表
func (s *sAdvertisement) PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (out *v1.AdvertisementPositionListRes, err error) {
	out = new(v1.AdvertisementPositionListRes)
	out.List = make([]v1.AdvertisementPositionItem, 0)

	var positions []entity.AdvertisementPosition
	err = dao.AdvertisementPosition.Ctx(ctx).
		Order(dao.AdvertisementPosition.Columns().Id).
		Scan(&positions)
	if err != nil {
		return nil, err
	}

	for _, position := range positions {
		out.List = append(out.List, v1.AdvertisementPositionItem{
			Id:           int(position.Id),
			PositionName: position.PositionName,
			PositionCode: position.PositionCode,
			Remark:       position.Remark,
		})
	}

	return out, nil
}

// PositionEdit 修改广告位置备注
func (s *sAdvertisement) PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (out *v1.AdvertisementPositionEditRes, err error) {
	out = new(v1.AdvertisementPositionEditRes)

	// 检查广告位置是否存在
	count, err := dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("Advertisement position does not exist")
	}

	// 更新备注
	_, err = dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().Id, req.Id).
		Data(do.AdvertisementPosition{
			Remark:     req.Remark,
			UpdateTime: gtime.Now().UnixMilli(),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	return out, nil
}

// List 广告列表
func (s *sAdvertisement) List(ctx context.Context, req *v1.AdvertisementListReq) (out *v1.AdvertisementListRes, err error) {
	out = new(v1.AdvertisementListRes)
	out.List = make([]v1.AdvertisementListItem, 0)
	out.Current = req.Current
	out.Offset = req.Offset

	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	md := dao.Advertisement.Ctx(ctx)

	// 根据广告位置过滤
	if len(req.PositionCode) > 0 {
		md = md.Where(dao.Advertisement.Columns().PositionCode, req.PositionCode)
	}

	// 根据状态过滤
	if req.Status != nil {
		md = md.Where(dao.Advertisement.Columns().Status, *req.Status)
	}

	// 根据名称搜索（需要在多语言表中搜索）
	if len(req.Name) > 0 {
		subQuery := dao.AdvertisementLanguages.Ctx(ctx).
			WhereLike(dao.AdvertisementLanguages.Columns().Title, "%"+req.Name+"%").
			Fields(dao.AdvertisementLanguages.Columns().AdvertisementId)
		md = md.WhereIn(dao.Advertisement.Columns().Id, subQuery)
	}

	// 分页查询
	out.Total, err = md.Count()
	if err != nil {
		return out, err
	}

	var advertisements []entity.Advertisement
	err = md.Page(req.Current, req.PageSize).
		OrderDesc(dao.Advertisement.Columns().CreateTime).
		Scan(&advertisements)
	if err != nil {
		return out, err
	}

	if len(advertisements) == 0 {
		return out, nil
	}

	// 获取广告位置信息
	var positionCodes []string
	for _, ad := range advertisements {
		positionCodes = append(positionCodes, ad.PositionCode)
	}
	var positions []entity.AdvertisementPosition
	err = dao.AdvertisementPosition.Ctx(ctx).
		WhereIn(dao.AdvertisementPosition.Columns().PositionCode, positionCodes).
		Scan(&positions)
	if err != nil {
		return out, err
	}

	positionMap := make(map[string]string)
	for _, position := range positions {
		positionMap[position.PositionCode] = position.PositionName
	}

	var adIds []int
	for _, ad := range advertisements {
		adIds = append(adIds, int(ad.Id))
	}
	var languages []entity.AdvertisementLanguages
	err = dao.AdvertisementLanguages.Ctx(ctx).
		WhereIn(dao.AdvertisementLanguages.Columns().AdvertisementId, adIds).
		Scan(&languages)
	if err != nil {
		return out, err
	}

	languageMap := make(map[int]map[int]entity.AdvertisementLanguages)
	for _, lang := range languages {
		if languageMap[int(lang.AdvertisementId)] == nil {
			languageMap[int(lang.AdvertisementId)] = make(map[int]entity.AdvertisementLanguages)
		}
		languageMap[int(lang.AdvertisementId)][int(lang.LanguageId)] = lang
	}

	// 返回数据
	for _, ad := range advertisements {
		item := v1.AdvertisementListItem{
			AdvertisementId: int(ad.Id),
			PositionCode:    ad.PositionCode,
			PositionName:    positionMap[ad.PositionCode],
			Status:          int(ad.Status),
			LanguageArr:     []v1.LanguageArrItem{},
			CreateTime:      int64(ad.CreateTime),
		}

		// 设置当前语言的标题
		if langData, ok := languageMap[int(ad.Id)][currentLang]; ok {
			item.Title = langData.Title
		}

		// 多语言支持信息
		for _, langType := range []int{consts.LangZh, consts.LangEn, consts.LangId} {
			isSupport := 0
			if _, ok := languageMap[int(ad.Id)][langType]; ok {
				isSupport = 1
			}
			item.LanguageArr = append(item.LanguageArr, v1.LanguageArrItem{
				LanguageType:     langType,
				LanguageTypeText: consts.GetLangText(langType),
				IsSupport:        isSupport,
			})
		}

		out.List = append(out.List, item)
	}

	return out, nil
}

// One 广告详情
func (s *sAdvertisement) One(ctx context.Context, req *v1.AdvertisementOneReq) (out *v1.AdvertisementOneRes, err error) {
	out = new(v1.AdvertisementOneRes)

	// 获取广告基本信息
	var advertisement entity.Advertisement
	err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
		Scan(&advertisement)
	if err != nil {
		return nil, err
	}
	if advertisement.Id == 0 {
		return nil, gerror.New("Advertisement does not exist")
	}

	// 获取多语言信息
	var languages []entity.AdvertisementLanguages
	err = dao.AdvertisementLanguages.Ctx(ctx).
		Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.AdvertisementId).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 获取Banner图片信息
	var banners []entity.AdvertisementBanner
	err = dao.AdvertisementBanner.Ctx(ctx).
		Where(dao.AdvertisementBanner.Columns().AdvertisementId, req.AdvertisementId).
		OrderAsc(dao.AdvertisementBanner.Columns().Id).
		Scan(&banners)
	if err != nil {
		return nil, err
	}

	// 按语言类型分组Banner
	bannerMap := make(map[int][]entity.AdvertisementBanner)
	for _, banner := range banners {
		bannerMap[int(banner.LanguageId)] = append(bannerMap[int(banner.LanguageId)], banner)
	}

	// 组装多语言信息
	var languageDetails []v1.AdvertisementLanguageDetail
	for _, lang := range languages {
		// 获取该语言对应的Banner
		var bannerDetails []v1.BannerDetail
		if langBanners, exists := bannerMap[int(lang.LanguageId)]; exists {
			for _, banner := range langBanners {
				bannerDetails = append(bannerDetails, v1.BannerDetail{
					BannerId: int(banner.Id),
					ImageUrl: banner.ImageUrl,
					LinkUrl:  banner.LinkUrl,
				})
			}
		}

		languageDetails = append(languageDetails, v1.AdvertisementLanguageDetail{
			LanguageType:     int(lang.LanguageId),
			Title:            lang.Title,
			DisplayType:      int(lang.DisplayType),
			CarouselInterval: int(lang.CarouselInterval),
			Banners:          bannerDetails,
		})
	}

	out.Advertisement = v1.AdvertisementDetail{
		AdvertisementId: int(advertisement.Id),
		PositionCode:    advertisement.PositionCode,
		Status:          int(advertisement.Status),
		Languages:       languageDetails,
	}

	return out, nil
}

// Add 新增广告
func (s *sAdvertisement) Add(ctx context.Context, req *v1.AdvertisementAddReq) (out *v1.AdvertisementAddRes, err error) {
	out = new(v1.AdvertisementAddRes)

	// 验证广告位置是否存在
	count, err := dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().PositionCode, req.PositionCode).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("Advertisement position does not exist")
	}

	// 验证多语言内容的语言类型不能重复
	languageMap := make(map[int]bool)
	for _, lang := range req.Languages {
		if languageMap[lang.LanguageType] {
			return nil, gerror.New("Language type duplicate")
		}
		languageMap[lang.LanguageType] = true
	}

	// 获取当前管理员信息
	adminId := uint(0)
	admin, err := service.Utility().GetSelf(ctx)
	if err == nil {
		adminId = admin.Id
	}

	var advertisementId int64
	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入广告主表
		result, err1 := tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Data(do.Advertisement{
				PositionCode: req.PositionCode,
				Status:       0, // 默认禁用状态
				AdminId:      adminId,
				CreateTime:   gtime.Now().UnixMilli(),
				UpdateTime:   gtime.Now().UnixMilli(),
			}).
			Insert()
		if err1 != nil {
			return err1
		}

		advertisementId, err1 = result.LastInsertId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容和对应的Banner
		for _, lang := range req.Languages {
			// 插入多语言内容
			_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
				Data(do.AdvertisementLanguages{
					AdvertisementId:  advertisementId,
					LanguageId:       lang.LanguageType,
					Title:            lang.Title,
					Description:      "", // 其实是预留字段而已
					DisplayType:      lang.DisplayType,
					CarouselInterval: lang.CarouselInterval,
					AdminId:          adminId,
					CreateTime:       gtime.Now().UnixMilli(),
					UpdateTime:       gtime.Now().UnixMilli(),
				}).
				Insert()
			if err1 != nil {
				return err1
			}

			// 插入该语言对应的Banner图片
			if len(lang.Banners) > 0 {
				var banners []do.AdvertisementBanner
				for i, banner := range lang.Banners {
					banners = append(banners, do.AdvertisementBanner{
						AdvertisementId: advertisementId,
						LanguageId:      lang.LanguageType,
						ImageUrl:        banner.ImageUrl,
						LinkUrl:         banner.LinkUrl,
						SortOrder:       i + 1, // 使用索引作为排序
						CreateTime:      gtime.Now().UnixMilli(),
						UpdateTime:      gtime.Now().UnixMilli(),
					})
				}

				_, err1 = tx.Model(dao.AdvertisementBanner.Table()).Ctx(ctx).
					Data(banners).
					Insert()
				if err1 != nil {
					return err1
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.AdvertisementId = int(advertisementId)

	return out, nil
}

// Edit 编辑广告
func (s *sAdvertisement) Edit(ctx context.Context, req *v1.AdvertisementEditReq) (out *v1.AdvertisementEditRes, err error) {
	out = new(v1.AdvertisementEditRes)

	// 验证广告是否存在
	count, err := dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("Advertisement does not exist")
	}

	// 验证广告位置是否存在
	count, err = dao.AdvertisementPosition.Ctx(ctx).
		Where(dao.AdvertisementPosition.Columns().PositionCode, req.PositionCode).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("Advertisement position does not exist")
	}

	// 验证多语言内容的语言类型不能重复
	languageMap := make(map[int]bool)
	for _, lang := range req.Languages {
		if languageMap[lang.LanguageType] {
			return nil, gerror.New("Language type duplicate")
		}
		languageMap[lang.LanguageType] = true
	}

	// 获取当前管理员信息
	adminId := uint(0)
	admin, err := service.Utility().GetSelf(ctx)
	if err == nil {
		adminId = admin.Id
	}

	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 更新广告主表
		_, err1 := tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
			Data(do.Advertisement{
				PositionCode: req.PositionCode,
				UpdateTime:   gtime.Now().UnixMilli(),
			}).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除原有的多语言内容
		_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
			Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.AdvertisementId).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除原有的Banner图片
		_, err1 = tx.Model(dao.AdvertisementBanner.Table()).Ctx(ctx).
			Where(dao.AdvertisementBanner.Columns().AdvertisementId, req.AdvertisementId).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容和对应的Banner
		for _, lang := range req.Languages {
			// 插入多语言内容
			_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
				Data(do.AdvertisementLanguages{
					AdvertisementId:  req.AdvertisementId,
					LanguageId:       lang.LanguageType,
					Title:            lang.Title,
					Description:      "", // 新结构中没有Description字段
					DisplayType:      lang.DisplayType,
					CarouselInterval: lang.CarouselInterval,
					AdminId:          adminId,
					CreateTime:       gtime.Now().UnixMilli(),
					UpdateTime:       gtime.Now().UnixMilli(),
				}).
				Insert()
			if err1 != nil {
				return err1
			}

			// 插入该语言对应的Banner图片
			if len(lang.Banners) > 0 {
				var banners []do.AdvertisementBanner
				for i, banner := range lang.Banners {
					banners = append(banners, do.AdvertisementBanner{
						AdvertisementId: req.AdvertisementId,
						LanguageId:      lang.LanguageType,
						ImageUrl:        banner.ImageUrl,
						LinkUrl:         banner.LinkUrl,
						SortOrder:       i + 1, // 使用索引作为排序
						CreateTime:      gtime.Now().UnixMilli(),
						UpdateTime:      gtime.Now().UnixMilli(),
					})
				}

				_, err1 = tx.Model(dao.AdvertisementBanner.Table()).Ctx(ctx).
					Data(banners).
					Insert()
				if err1 != nil {
					return err1
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

// Delete 删除广告
func (s *sAdvertisement) Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (out *v1.AdvertisementDeleteRes, err error) {
	out = new(v1.AdvertisementDeleteRes)

	// 验证广告是否存在
	count, err := dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("Advertisement does not exist")
	}

	err = dao.Advertisement.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除Banner图片
		_, err1 := tx.Model(dao.AdvertisementBanner.Table()).Ctx(ctx).
			Where(dao.AdvertisementBanner.Columns().AdvertisementId, req.AdvertisementId).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除多语言内容
		_, err1 = tx.Model(dao.AdvertisementLanguages.Table()).Ctx(ctx).
			Where(dao.AdvertisementLanguages.Columns().AdvertisementId, req.AdvertisementId).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除广告主表
		_, err1 = tx.Model(dao.Advertisement.Table()).Ctx(ctx).
			Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

// ToggleStatus 切换广告状态
func (s *sAdvertisement) ToggleStatus(ctx context.Context, req *v1.AdvertisementToggleStatusReq) (out *v1.AdvertisementToggleStatusRes, err error) {
	out = new(v1.AdvertisementToggleStatusRes)

	// 验证广告是否存在
	var advertisement entity.Advertisement
	err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
		Scan(&advertisement)
	if err != nil {
		return nil, err
	}
	if advertisement.Id == 0 {
		return nil, gerror.New("Advertisement does not exist")
	}

	// 检查状态是否已经是目标状态
	if int(advertisement.Status) == req.Status {
		statusText := "offline"
		if req.Status == 1 {
			statusText = "online"
		}
		return nil, gerror.New("Advertisement is already " + statusText)
	}

	// 如果要上线，检查同一个广告位置是否已经有上线的广告（业务规则：同一个广告位置最多只能有一个上线状态的广告）
	if req.Status == 1 {
		count, err := dao.Advertisement.Ctx(ctx).
			Where(dao.Advertisement.Columns().PositionCode, advertisement.PositionCode).
			Where(dao.Advertisement.Columns().Status, 1).
			WhereNot(dao.Advertisement.Columns().Id, req.AdvertisementId).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("There is already an online advertisement in the same advertisement position, please offline other advertisements first")
		}
	}

	// 更新状态
	_, err = dao.Advertisement.Ctx(ctx).
		Where(dao.Advertisement.Columns().Id, req.AdvertisementId).
		Data(do.Advertisement{
			Status:     uint(req.Status),
			UpdateTime: gtime.Now().UnixMilli(),
		}).
		Update()
	if err != nil {
		return nil, err
	}

	return out, nil
}
