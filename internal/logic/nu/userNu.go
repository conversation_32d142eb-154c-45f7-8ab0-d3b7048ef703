package nu

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/user_account_svc"
	do "gtcms/internal/model/do/user_account_svc"
	entity "gtcms/internal/model/entity/user_account_svc"
	"gtcms/internal/service"
	"gtcms/utility"
)

type sUserNu struct{}

func init() {
	service.RegisterUserNu(New())
}
func New() *sUserNu {
	return &sUserNu{}
}

func (f *sUserNu) UserNuList(ctx context.Context, req *v1.UserNuListReq) (res *v1.UserNuListRes, err error) {
	md := dao.UserNuId.Ctx(ctx)
	if req.RealName != "" {
		md = md.WhereLike(dao.UserNuId.Columns().RealName, "%"+req.RealName+"%")
	}

	if req.UserId > 0 {
		md = md.Where(dao.UserNuId.Columns().UserId, req.UserId)
	}

	if req.Gender != nil {
		md = md.Where(dao.UserNuId.Columns().Gender, req.Gender)
	}
	if req.Status > 0 {
		md = md.Where(dao.UserNuId.Columns().Status, req.Status)
	}
	if req.NickName != "" {
		userIdsModel := dao.UserInfo.Ctx(ctx).Where("nick_name like ?", "%"+req.NickName+"%").Fields("user_id")
		md = md.WhereIn(dao.UserNuId.Columns().UserId, userIdsModel)
	}
	if req.StartTime > 0 {
		md = md.WhereGTE(dao.UserNuId.Columns().ReviewedAt, req.StartTime).WhereLTE(dao.UserNuId.Columns().ReviewedAt, req.EndTime)
	}
	// todo 这个是不是该用区域id ?
	if req.WilayahPwnu != "" {
		md = md.Where(dao.UserNuId.Columns().WilayahPwnu, req.WilayahPwnu)
	}
	var userNus []*v1.UserNuListItem
	var total int

	err = md.Page(req.Current, req.PageSize).OrderDesc("id").ScanAndCount(&userNus, &total, false)
	if err != nil {
		return nil, err
	}

	var userInfos []*entity.UserInfo
	userIds := gutil.ListItemValuesUnique(userNus, "UserId")
	err = dao.UserInfo.Ctx(ctx).WhereIn(dao.UserInfo.Columns().UserId, userIds).Scan(&userInfos)
	if err != nil {
		return nil, err
	}

	userMap := utility.Slice2Map(userInfos, func(e *entity.UserInfo) uint64 {
		return e.UserId
	})
	for _, u := range userNus {
		if _, ok := userMap[u.UserId]; ok {
			u.Nickname = userMap[u.UserId].Nickname
		}
	}
	res = &v1.UserNuListRes{
		List:    userNus,
		ListRes: v1.ListRes{Current: req.Current, Total: total},
	}

	return
}

func (f *sUserNu) UserNuAudit(ctx context.Context, req *v1.UserNuAuditReq) (res *v1.UserNuAuditRes, err error) {
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}
	err = dao.UserNuAudit.Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		userNuAudits := make([]do.UserNuAudit, 0, len(req.Ids))
		for _, id := range req.Ids {
			userNuAudits = append(userNuAudits, do.UserNuAudit{
				UserNuId:         id,
				AuditStatus:      req.AuditStatus,
				AuditReason:      req.AuditReason,
				AuditAccount:     admin.Account,
				AuditAccountName: admin.NickName,
				CreateTime:       gtime.TimestampMilli(),
			})
		}

		_, err = dao.UserNuAudit.Ctx(ctx).Save(userNuAudits)

		if err != nil {
			return err
		}

		_, err = dao.UserNuId.Ctx(ctx).WhereIn(dao.UserNuId.Columns().Id, req.Ids).Update(
			do.UserNuId{
				Status: req.AuditStatus,
			})
		return err
	})

	return
}

func (f *sUserNu) UserNuOne(ctx context.Context, req *v1.UserNuOneReq) (res *v1.UserNuOneRes, err error) {

	err = dao.UserNuId.Ctx(ctx).Where(dao.UserNuId.Columns().Id, req.Id).Scan(&res)
	if err != nil {
		return
	}

	// 指针不会报 sql.ErrNoRows
	var audit *entity.UserNuAudit
	err = dao.UserNuAudit.Ctx(ctx).Where(dao.UserNuAudit.Columns().UserNuId, req.Id).Scan(&audit)

	if err != nil {
		return
	}
	if res == nil {
		return
	}
	res.Audit = audit

	// 用户信息应该必须有
	var userInfo *entity.UserInfo
	err = dao.UserNuId.Ctx(ctx).Where(dao.UserInfo.Columns().UserId, res.UserId).Scan(&userInfo)
	if err != nil {
		return
	}
	if userInfo != nil {
		res.Nickname = userInfo.Nickname
	}

	return

}
