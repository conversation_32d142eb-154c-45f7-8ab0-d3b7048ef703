package wisdom

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	model "gtcms/internal/model/islamic"
	"gtcms/internal/service"
)

type sWisdom struct{}

func init() {
	service.RegisterWisdom(New())
}

func New() *sWisdom {
	return &sWisdom{}
}

func (s *sWisdom) WisdomCateAdd(ctx context.Context, req *v1.WisdomCateCreateReq) (res *v1.WisdomCateCreateRes, err error) {
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}

	err = dao.WisdomCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		id, err := tx.Model(dao.WisdomCate.Table()).InsertAndGetId(do.WisdomCate{
			Remark:        req.Remark,
			Sort:          req.Sort,
			CreateAccount: admin.Account,
			CateCount:     0,
			CreateTime:    gtime.Now().UnixMilli(),
		})
		if err != nil {
			return err
		}
		var wisdomCateLanguages = make([]do.WisdomCateLanguage, 0, len(req.Item))
		isZh := 0
		isEn := 0
		isId := 0
		for _, item := range req.Item {
			wisdomCateLanguage := do.WisdomCateLanguage{
				WisdomCateId: id,
				Title:        item.Title,
				LanguageId:   item.LanguageId,
			}
			wisdomCateLanguages = append(wisdomCateLanguages, wisdomCateLanguage)

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}
		_, err = tx.Model(dao.WisdomCateLanguage.Table()).Insert(wisdomCateLanguages)
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.WisdomCate.Table()).Where("id = ?", id).Update(
			do.WisdomCate{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})

		return err
	})

	return
}

func (c *sWisdom) WisdomCateEdit(ctx context.Context, req *v1.WisdomCateEditReq) (res *v1.WisdomCateEditRes, err error) {

	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}

	err = dao.WisdomCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := tx.Model(dao.WisdomCate.Table()).Where("id = ?", req.Id).
			Update(do.WisdomCate{
				Remark:        req.Remark,
				Sort:          req.Sort,
				UpdateAccount: admin.Account,
				UpdateTime:    gtime.New().UnixMilli(),
			})
		if err != nil {
			return err
		}

		isZh := 0
		isEn := 0
		isId := 0
		for _, item := range req.Item {
			wisdomCateLanguage := do.WisdomCateLanguage{
				WisdomCateId: req.Id,
				Title:        item.Title,
				LanguageId:   item.LanguageId,
			}

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}

			//if item.Id > 0 {
			//	_, err = tx.Model(dao.WisdomCateLanguage.Table()).
			//		Data(wisdomCateLanguage).
			//		Where(dao.WisdomCateLanguage.Columns().Id, item.Id).
			//		Update()
			//} else {
			//	// 新增
			//	_, err = tx.Model(dao.WisdomCateLanguage.Table()).
			//		Data(wisdomCateLanguage).
			//		Insert()
			//}

			_, err = tx.Model(dao.WisdomCateLanguage.Table()).Data(wisdomCateLanguage).Save()

			if err != nil {
				return err
			}
		}

		_, err = tx.Model(dao.WisdomCate.Table()).Where("id = ?", req.Id).Update(
			do.WisdomCate{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})

		return err
	})

	return

}

func (c *sWisdom) WisdomCateOne(ctx context.Context, req *v1.WisdomCateOneReq) (res *v1.WisdomCateOneRes, err error) {
	var cate model.WisdomCateWithLanguage
	err = dao.WisdomCate.Ctx(ctx).
		Where(dao.WisdomCate.Columns().Id, req.Id).
		With(model.WisdomCateWithLanguage{}.Item). // 预加载一对多
		Scan(&cate)
	if err != nil {
		return
	}
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	for _, item := range cate.Item {
		if item.LanguageId == currentLang {
			cate.Title = item.Title
		}
	}
	return &v1.WisdomCateOneRes{
		WisdomCateWithLanguage: cate,
	}, nil

}

func (c *sWisdom) WisdomCateDelete(ctx context.Context, req *v1.WisdomCateDeleteReq) (res *v1.WisdomCateDeleteRes, err error) {
	err = dao.WisdomCate.Ctx(ctx).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		count, err := tx.Model(dao.Wisdom.Table()).
			Where(dao.Wisdom.Columns().WisdomCateId, req.Id).
			Count()
		if err != nil {
			return err
		}
		if count > 0 {
			err = gerror.NewCode(gcode.CodeInvalidOperation, gi18n.T(ctx, "wisdomCate.delete.InvalidOperation"))
			return err
		}

		_, err = tx.Model(dao.WisdomCate.Table()).Where(dao.WisdomCate.Columns().Id, req.Id).Delete()

		if err != nil {
			return err
		}
		_, err = tx.Model(dao.WisdomCateLanguage.Table()).Where(dao.WisdomCateLanguage.Columns().WisdomCateId, req.Id).Delete()

		return err

	})
	if err != nil {
		return nil, err
	}
	return
}

func (c *sWisdom) WisdomCateList(ctx context.Context, req *v1.WisdomCateListReq) (res *v1.WisdomCateListRes, err error) {
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	var cates []*model.WisdomCateWithLanguage
	var total int
	md := dao.WisdomCate.Ctx(ctx)
	smd := dao.WisdomCateLanguage.Ctx(ctx).Where(dao.WisdomCateLanguage.Columns().LanguageId, currentLang)
	if req.Title != "" {
		smd = smd.WhereLike(dao.WisdomCateLanguage.Columns().Title, "%"+req.Title+"%")
	}
	ids := smd.Fields(dao.WisdomCateLanguage.Columns().WisdomCateId)

	md = dao.WisdomCate.Ctx(ctx).WhereIn(dao.WisdomCate.Columns().Id, ids)

	err = md.Page(req.Current, req.PageSize).
		With(model.WisdomCateWithLanguage{}.Item). // 预加载一对多
		OrderDesc(dao.WisdomCate.Columns().Id).
		Page(req.Current, req.PageSize).
		ScanAndCount(&cates, &total, false)
	if err != nil {
		return
	}

	for _, cate := range cates {
		for _, cateL := range cate.Item {
			if cateL.LanguageId == currentLang {
				cate.Title = cateL.Title
			}
		}
		cate.Item = nil
	}

	res = &v1.WisdomCateListRes{
		List: cates,
	}
	res.ListRes.Total = total
	res.ListRes.Current = req.Current
	return

}

func (c *sWisdom) WisdomAdd(ctx context.Context, req *v1.WisdomCreateReq) (res *v1.WisdomCreateRes, err error) {
	err = dao.Wisdom.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		lastId, err := tx.Model(dao.Wisdom.Table()).Ctx(ctx).Data(
			do.Wisdom{Sort: req.Sort, WisdomCateId: req.CateId, ArticleId: req.ArticleId,
				IsPublish:       consts.FaqStatusPublished,
				PublishTime:     gtime.Now().UnixMilli(),
				ArticleCategory: req.ArticleCategory,
				CreateTime:      gtime.Now().UnixMilli(),
			}).
			InsertAndGetId()
		if err != nil {
			return err
		}
		//  表新增。
		var widsdomLanguages = make([]do.WisdomLanguage, 0, len(req.Item))

		isZh := 0
		isEn := 0
		isId := 0

		for _, item := range req.Item {
			wisdomLanguage := do.WisdomLanguage{
				WisdomId:   lastId,
				LanguageId: item.LanguageId,
				Title:      item.Title,
				ImageUrl:   item.ImageUrl,
			}
			widsdomLanguages = append(widsdomLanguages, wisdomLanguage)

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		_, err = tx.Model(dao.WisdomLanguage.Table()).Ctx(ctx).Data(widsdomLanguages).Insert()
		if err != nil {
			return err
		}
		// cate  count 加一
		_, err = tx.Model(dao.WisdomCate.Table()).Ctx(ctx).Where("id = ?", req.CateId).
			Increment(dao.WisdomCate.Columns().CateCount, 1)
		if err != nil {
			return err
		}
		// 更新主表语言。
		_, err = tx.Model(dao.Wisdom.Table()).Ctx(ctx).Where(dao.Wisdom.Columns().Id, lastId).
			Update(do.Wisdom{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		return err
	})

	return

}

func (c *sWisdom) WisdomEdit(ctx context.Context, req *v1.WisdomEditReq) (res *v1.WisdomEditRes, err error) {
	err = dao.Wisdom.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

		_, err = tx.Model(dao.Wisdom.Table()).Ctx(ctx).Where("id = ?", req.Id).
			Data(
				do.Wisdom{Sort: req.Sort, WisdomCateId: req.CateId, ArticleId: req.ArticleId, ArticleCategory: req.ArticleCategory,
					UpdateTime: gtime.Now().UnixMilli()}).
			Update()
		if err != nil {
			return err
		}
		//  表新增。

		isZh := 0
		isEn := 0
		isId := 0

		for _, item := range req.Item {
			wisdomLanguage := do.WisdomLanguage{
				WisdomId:   req.Id,
				LanguageId: item.LanguageId,
				Title:      item.Title,
				ImageUrl:   item.ImageUrl,
			}
			_, err = tx.Model(dao.WisdomLanguage.Table()).Data(wisdomLanguage).Save()

			if err != nil {
				return err
			}
			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		// 更新主表语言。
		_, err = tx.Model(dao.Wisdom.Table()).Ctx(ctx).Where(dao.Wisdom.Columns().Id, req.Id).
			Update(do.Wisdom{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		return err
	})

	return
}

func (c *sWisdom) WisdomDelete(ctx context.Context, req *v1.WisdomDeleteReq) (res *v1.WisdomDeleteRes, err error) {
	err = dao.Wisdom.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var wisdoms []*entity.Wisdom
		err = tx.Model(dao.Wisdom.Table()).Ctx(ctx).WhereIn("id", req.Ids).Scan(&wisdoms)
		if err != nil {
			return err
		}
		cateIds := gdb.ListItemValuesUnique(wisdoms, "WisdomCateId")

		_, err = tx.Model(dao.Wisdom.Table()).Ctx(ctx).WhereIn("id", req.Ids).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.WisdomLanguage.Table()).Ctx(ctx).WhereIn("wisdom_id", req.Ids).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.WisdomCate.Table()).Ctx(ctx).WhereIn("id", cateIds).
			Decrement(dao.WisdomCate.Columns().CateCount, 1)

		return err
	})

	return
}

func (c *sWisdom) WisdomOne(ctx context.Context, req *v1.WisdomOneReq) (res *v1.WisdomOneRes, err error) {

	err = dao.Wisdom.Ctx(ctx).Where("id", req.Id).
		With(model.WisdomWithLanguage{}.Item).Scan(&res)
	if err != nil {
		return
	}

	return
}

func (c *sWisdom) WisdomList(ctx context.Context, req *v1.WisdomListReq) (res *v1.WisdomListRes, err error) {
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	md := dao.Wisdom.Ctx(ctx)
	sub := dao.WisdomLanguage.Ctx(ctx)
	if req.Title != "" {
		sub = sub.WhereLike(dao.WisdomLanguage.Columns().Title, "%"+req.Title+"%")
	}
	ids := sub.Where(dao.WisdomLanguage.Columns().LanguageId, currentLang).Fields(dao.WisdomLanguage.Columns().WisdomId)
	md = md.WhereIn(dao.Wisdom.Columns().Id, ids)
	if req.CateId > 0 {
		md = md.Where(dao.Wisdom.Columns().WisdomCateId, req.CateId)
	}
	md = md.WhereIn("id", ids)

	if req.StartTime > 0 {
		md = md.WhereGTE(dao.Wisdom.Columns().CreateTime, req.StartTime).
			WhereLTE(dao.Wisdom.Columns().CreateTime, req.EndTime)
	}

	if req.IsPublish != nil {
		md = md.Where(dao.Wisdom.Columns().IsPublish, req.IsPublish)
	}

	var items []*model.WisdomWithLanguage
	var total int
	err = md.With(model.WisdomWithLanguage{}.Item, model.WisdomWithLanguage{}.Cate, model.WisdomWithLanguage{}.Article).
		OrderDesc(dao.Wisdom.Columns().Id).
		ScanAndCount(&items, &total, false)
	if err != nil {
		return
	}

	cateIds := gutil.ListItemValuesUnique(items, "WisdomCateId")
	fmt.Println(cateIds)
	for _, w := range items {
		for _, wl := range w.Item {
			if wl.LanguageId == currentLang {
				w.Title = wl.Title
			}
		}
		w.Item = nil

		for _, cl := range w.Cate {
			if cl.LanguageId == currentLang {
				w.CateTitle = cl.Title
			}
		}
		w.Cate = nil

		for _, al := range w.Article {
			if al.LanguageId == uint(currentLang) {
				w.ArticleTitle = al.Name
			}
		}
		w.Article = nil
	}

	res = &v1.WisdomListRes{
		List: items,
		ListRes: v1.ListRes{
			Current: req.Current,
			Offset:  req.Offset,
			Total:   total,
		},
	}

	return

}

func (c *sWisdom) WisdomCateAll(ctx context.Context, req *v1.WisdomCateAllReq) (res *v1.WisdomCateAllRes, err error) {
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))

	var cates []*model.WisdomCateWithLanguage
	md := dao.WisdomCate.Ctx(ctx)
	smd := dao.WisdomCateLanguage.Ctx(ctx).Where(dao.WisdomCateLanguage.Columns().LanguageId, currentLang)
	if req.Title != "" {
		smd = smd.WhereLike(dao.WisdomCateLanguage.Columns().Title, "%"+req.Title+"%")
	}
	ids := smd.Fields(dao.WisdomCateLanguage.Columns().WisdomCateId)

	md = dao.WisdomCate.Ctx(ctx).WhereIn(dao.WisdomCate.Columns().Id, ids)

	err = md.With(model.WisdomCateWithLanguage{}.Item). // 预加载一对多
								Order("id desc").
								Scan(&cates)
	if err != nil {
		return nil, err
	}

	for _, cate := range cates {
		for _, cateL := range cate.Item {
			if cateL.LanguageId == currentLang {
				cate.Title = cateL.Title
			}
		}
		cate.Item = nil
	}

	res = &v1.WisdomCateAllRes{
		List: cates,
	}

	return

}

func (f *sWisdom) Publish(ctx context.Context, req *v1.WisdomPublishReq) (res *v1.WisdomPublishRes, err error) {
	doData := do.Wisdom{
		IsPublish: req.IsPublish,
	}
	if req.IsPublish == consts.FaqStatusPublished {
		doData.PublishTime = gtime.New().UnixMilli()
	}
	_, err = dao.Wisdom.Ctx(ctx).WhereIn(dao.Wisdom.Columns().Id, req.Ids).
		WhereNot(dao.Wisdom.Columns().IsPublish, req.IsPublish).Update(doData)
	if err != nil {
		return nil, err
	}
	return
}
