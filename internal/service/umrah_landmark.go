// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IUmrahLandmark interface {
		TypeList(ctx context.Context, req *v1.UmrahLandmarkTypeListReq) (out *v1.UmrahLandmarkTypeListRes, err error)
		TypeAdd(ctx context.Context, req *v1.UmrahLandmarkTypeCreateReq) (out *v1.UmrahLandmarkTypeCreateRes, err error)
		TypeEdit(ctx context.Context, req *v1.UmrahLandmarkTypeEditReq) (out *v1.UmrahLandmarkTypeEditRes, err error)
		TypeOne(ctx context.Context, req *v1.UmrahLandmarkTypeOneReq) (out *v1.UmrahLandmarkTypeOneRes, err error)
		TypeDelete(ctx context.Context, req *v1.UmrahLandmarkTypeDeleteReq) (out *v1.UmrahLandmarkTypeDeleteRes, err error)
		List(ctx context.Context, req *v1.UmrahLandmarkListReq) (out *v1.UmrahLandmarkListRes, err error)
		Add(ctx context.Context, req *v1.UmrahLandmarkCreateReq) (out *v1.UmrahLandmarkCreateRes, err error)
		// Edit 编辑地标
		Edit(ctx context.Context, req *v1.UmrahLandmarkEditReq) (out *v1.UmrahLandmarkEditRes, err error)
		One(ctx context.Context, req *v1.UmrahLandmarkOneReq) (out *v1.UmrahLandmarkOneRes, err error)
		Delete(ctx context.Context, req *v1.UmrahLandmarkDeleteReq) (out *v1.UmrahLandmarkDeleteRes, err error)
	}
)

var (
	localUmrahLandmark IUmrahLandmark
)

func UmrahLandmark() IUmrahLandmark {
	if localUmrahLandmark == nil {
		panic("implement not found for interface IUmrahLandmark, forgot register?")
	}
	return localUmrahLandmark
}

func RegisterUmrahLandmark(i IUmrahLandmark) {
	localUmrahLandmark = i
}
