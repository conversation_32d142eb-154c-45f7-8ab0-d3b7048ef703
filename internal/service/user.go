// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IUser interface {
		List(ctx context.Context, in *v1.UserListReq) (res *v1.UserListRes, err error)
		Info(ctx context.Context, in *v1.UserInfoReq) (res *v1.UserInfoRes, err error)
		BatchBan(ctx context.Context, in *v1.BatchBanReq) (res *v1.EmptyDataRes, err error)
		UserLoginLog(ctx context.Context, in *v1.UserLoginLogReq) (res *v1.UserLoginLogRes, err error)
		CollectList(ctx context.Context, in *v1.CollectListReq) (res *v1.RecordListRes, err error)
		ReadList(ctx context.Context, in *v1.ReadListReq) (res *v1.RecordListRes, err error)
		ShareList(ctx context.Context, in *v1.ShareListReq) (res *v1.RecordListRes, err error)
	}
)

var (
	localUser IUser
)

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}
