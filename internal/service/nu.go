// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IUserNu interface {
		UserNuList(ctx context.Context, req *v1.UserNuListReq) (res *v1.UserNuListRes, err error)
		UserNuAudit(ctx context.Context, req *v1.UserNuAuditReq) (res *v1.UserNuAuditRes, err error)
		UserNuOne(ctx context.Context, req *v1.UserNuOneReq) (res *v1.UserNuOneRes, err error)
	}
)

var (
	localUserNu IUserNu
)

func UserNu() IUserNu {
	if localUserNu == nil {
		panic("implement not found for interface IUserNu, forgot register?")
	}
	return localUserNu
}

func RegisterUserNu(i IUserNu) {
	localUserNu = i
}
