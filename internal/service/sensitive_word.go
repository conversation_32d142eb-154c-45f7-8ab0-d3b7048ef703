// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ISensitiveWord interface {
		// 敏感词分类添加
		CategoryAdd(ctx context.Context, req *v1.SensitiveWordCategoryAddReq) error
		// 敏感词分类详情
		CategoryInfo(ctx context.Context, req *v1.SensitiveWordCategoryInfoReq) (out *v1.SensitiveWordCategoryInfoRes, err error)
		// 敏感词分类列表
		CategoryList(ctx context.Context, req *v1.SensitiveWordCategoryListReq) (out *v1.SensitiveWordCategoryListRes, err error)
		// 敏感词分类编辑
		CategoryEdit(ctx context.Context, req *v1.SensitiveWordCategoryEditReq) error
		// 敏感词分类删除
		CategoryDel(ctx context.Context, req *v1.SensitiveWordCategoryDelReq) error
		// 敏感词分类添加
		Add(ctx context.Context, req *v1.SensitiveWordAddReq) error
		// 敏感词分类详情
		Info(ctx context.Context, req *v1.SensitiveWordInfoReq) (out *v1.SensitiveWordInfoRes, err error)
		// 敏感词分类编辑
		Edit(ctx context.Context, req *v1.SensitiveWordEditReq) error
		// 敏感词分类列表
		List(ctx context.Context, req *v1.SensitiveWordListReq) (out *v1.SensitiveWordListRes, err error)
		// 删除敏感词
		Delete(ctx context.Context, req *v1.SensitiveWordDelReq) error
	}
)

var (
	localSensitiveWord ISensitiveWord
)

func SensitiveWord() ISensitiveWord {
	if localSensitiveWord == nil {
		panic("implement not found for interface ISensitiveWord, forgot register?")
	}
	return localSensitiveWord
}

func RegisterSensitiveWord(i ISensitiveWord) {
	localSensitiveWord = i
}
