// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IHajiNewsTag interface {
		// Add 新增朝觐新闻标签
		Add(ctx context.Context, req *v1.HajiNewsTagAddReq) (err error)
		// Edit 编辑朝觐新闻标签
		Edit(ctx context.Context, req *v1.HajiNewsTagEditReq) (err error)
		// Info 获取朝觐新闻标签详情
		Info(ctx context.Context, req *v1.HajiNewsTagInfoReq) (out *v1.HajiNewsTagInfoRes, err error)
		// List 获取朝觐新闻标签列表
		List(ctx context.Context, req *v1.HajiNewsTagListReq) (out *v1.HajiNewsTagListRes, err error)
		// Delete 删除朝觐新闻标签
		Delete(ctx context.Context, req *v1.HajiNewsTagDeleteReq) (err error)
	}
)

var (
	localHajiNewsTag IHajiNewsTag
)

func HajiNewsTag() IHajiNewsTag {
	if localHajiNewsTag == nil {
		panic("implement not found for interface IHajiNewsTag, forgot register?")
	}
	return localHajiNewsTag
}

func RegisterHajiNewsTag(i IHajiNewsTag) {
	localHajiNewsTag = i
}
