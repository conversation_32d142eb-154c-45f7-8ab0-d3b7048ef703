// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IAdvertisement interface {
		// PositionList 广告位置列表
		PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (out *v1.AdvertisementPositionListRes, err error)
		// PositionEdit 修改广告位置备注
		PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (out *v1.AdvertisementPositionEditRes, err error)
		// List 广告列表
		List(ctx context.Context, req *v1.AdvertisementListReq) (out *v1.AdvertisementListRes, err error)
		// One 广告详情
		One(ctx context.Context, req *v1.AdvertisementOneReq) (out *v1.AdvertisementOneRes, err error)
		// Add 新增广告
		Add(ctx context.Context, req *v1.AdvertisementAddReq) (out *v1.AdvertisementAddRes, err error)
		// Edit 编辑广告
		Edit(ctx context.Context, req *v1.AdvertisementEditReq) (out *v1.AdvertisementEditRes, err error)
		// Delete 删除广告
		Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (out *v1.AdvertisementDeleteRes, err error)
		// ToggleStatus 切换广告状态
		ToggleStatus(ctx context.Context, req *v1.AdvertisementToggleStatusReq) (out *v1.AdvertisementToggleStatusRes, err error)
	}
)

var (
	localAdvertisement IAdvertisement
)

func Advertisement() IAdvertisement {
	if localAdvertisement == nil {
		panic("implement not found for interface IAdvertisement, forgot register?")
	}
	return localAdvertisement
}

func RegisterAdvertisement(i IAdvertisement) {
	localAdvertisement = i
}
