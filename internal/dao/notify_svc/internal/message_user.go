// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MessageUserDao is the data access object for the table message_user.
type MessageUserDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  MessageUserColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// MessageUserColumns defines and stores column names for the table message_user.
type MessageUserColumns struct {
	Id         string //
	MessageId  string // 消息id
	UserId     string // 收件人id
	IsRead     string // 是否已读
	ReadTime   string // 读件时间
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

// messageUserColumns holds the columns for the table message_user.
var messageUserColumns = MessageUserColumns{
	Id:         "id",
	MessageId:  "message_id",
	UserId:     "user_id",
	IsRead:     "is_read",
	ReadTime:   "read_time",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewMessageUserDao creates and returns a new DAO object for table data access.
func NewMessageUserDao(handlers ...gdb.ModelHandler) *MessageUserDao {
	return &MessageUserDao{
		group:    "notify_svc",
		table:    "message_user",
		columns:  messageUserColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MessageUserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MessageUserDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MessageUserDao) Columns() MessageUserColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MessageUserDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MessageUserDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MessageUserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
