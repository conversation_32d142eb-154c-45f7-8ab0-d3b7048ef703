// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserMessageTypeDao is the data access object for the table user_message_type.
type UserMessageTypeDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  UserMessageTypeColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// UserMessageTypeColumns defines and stores column names for the table user_message_type.
type UserMessageTypeColumns struct {
	Id          string //
	Name        string // 分类名字
	IconUrl     string // 分类图标
	CreatedTime string // 创建时间
	UpdatedTime string // 更新时间
}

// userMessageTypeColumns holds the columns for the table user_message_type.
var userMessageTypeColumns = UserMessageTypeColumns{
	Id:          "id",
	Name:        "name",
	IconUrl:     "icon_url",
	CreatedTime: "created_time",
	UpdatedTime: "updated_time",
}

// NewUserMessageTypeDao creates and returns a new DAO object for table data access.
func NewUserMessageTypeDao(handlers ...gdb.ModelHandler) *UserMessageTypeDao {
	return &UserMessageTypeDao{
		group:    "notify_svc",
		table:    "user_message_type",
		columns:  userMessageTypeColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserMessageTypeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserMessageTypeDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserMessageTypeDao) Columns() UserMessageTypeColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserMessageTypeDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserMessageTypeDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserMessageTypeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
