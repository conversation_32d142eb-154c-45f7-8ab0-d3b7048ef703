// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package notify_svc

import (
	"gtcms/internal/dao/notify_svc/internal"
)

// messageDao is the data access object for the table message.
// You can define custom methods on it to extend its functionality as needed.
type messageDao struct {
	*internal.MessageDao
}

var (
	// Message is a globally accessible object for table message operations.
	Message = messageDao{internal.NewMessageDao()}
)

// Add your custom methods and functionality below.
