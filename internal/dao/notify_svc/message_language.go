// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package notify_svc

import (
	"gtcms/internal/dao/notify_svc/internal"
)

// messageLanguageDao is the data access object for the table message_language.
// You can define custom methods on it to extend its functionality as needed.
type messageLanguageDao struct {
	*internal.MessageLanguageDao
}

var (
	// MessageLanguage is a globally accessible object for table message_language operations.
	MessageLanguage = messageLanguageDao{internal.NewMessageLanguageDao()}
)

// Add your custom methods and functionality below.
