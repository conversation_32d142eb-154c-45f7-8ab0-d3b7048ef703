// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package notify_svc

import (
	"gtcms/internal/dao/notify_svc/internal"
)

// messageTypeDao is the data access object for the table message_type.
// You can define custom methods on it to extend its functionality as needed.
type messageTypeDao struct {
	*internal.MessageTypeDao
}

var (
	// MessageType is a globally accessible object for table message_type operations.
	MessageType = messageTypeDao{internal.NewMessageTypeDao()}
)

// Add your custom methods and functionality below.
