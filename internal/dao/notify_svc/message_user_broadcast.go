// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package notify_svc

import (
	"gtcms/internal/dao/notify_svc/internal"
)

// messageUserBroadcastDao is the data access object for the table message_user_broadcast.
// You can define custom methods on it to extend its functionality as needed.
type messageUserBroadcastDao struct {
	*internal.MessageUserBroadcastDao
}

var (
	// MessageUserBroadcast is a globally accessible object for table message_user_broadcast operations.
	MessageUserBroadcast = messageUserBroadcastDao{internal.NewMessageUserBroadcastDao()}
)

// Add your custom methods and functionality below.
