// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// doaReadDao is the data access object for the table doa_read.
// You can define custom methods on it to extend its functionality as needed.
type doaReadDao struct {
	*internal.DoaReadDao
}

var (
	// DoaRead is a globally accessible object for table doa_read operations.
	DoaRead = doaReadDao{internal.NewDoaReadDao()}
)

// Add your custom methods and functionality below.
