// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// advertisementBannerDao is the data access object for the table advertisement_banner.
// You can define custom methods on it to extend its functionality as needed.
type advertisementBannerDao struct {
	*internal.AdvertisementBannerDao
}

var (
	// AdvertisementBanner is a globally accessible object for table advertisement_banner operations.
	AdvertisementBanner = advertisementBannerDao{internal.NewAdvertisementBannerDao()}
)

// Add your custom methods and functionality below.
