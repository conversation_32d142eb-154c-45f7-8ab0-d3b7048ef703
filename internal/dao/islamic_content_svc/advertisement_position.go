// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// advertisementPositionDao is the data access object for the table advertisement_position.
// You can define custom methods on it to extend its functionality as needed.
type advertisementPositionDao struct {
	*internal.AdvertisementPositionDao
}

var (
	// AdvertisementPosition is a globally accessible object for table advertisement_position operations.
	AdvertisementPosition = advertisementPositionDao{internal.NewAdvertisementPositionDao()}
)

// Add your custom methods and functionality below.
