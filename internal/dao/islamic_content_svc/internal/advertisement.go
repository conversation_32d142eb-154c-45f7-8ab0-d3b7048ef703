// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementDao is the data access object for the table advertisement.
type AdvertisementDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  AdvertisementColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// AdvertisementColumns defines and stores column names for the table advertisement.
type AdvertisementColumns struct {
	Id           string // 主键ID
	PositionCode string // 广告位置编码
	SortOrder    string // 排序权重，数字越小越靠前
	Status       string // 状态: 0-禁用, 1-启用
	StartTime    string // 开始时间戳(毫秒)
	EndTime      string // 结束时间戳(毫秒)
	AdminId      string // 创建管理员ID
	CreateTime   string // 创建时间(毫秒时间戳)
	UpdateTime   string // 更新时间(毫秒时间戳)
}

// advertisementColumns holds the columns for the table advertisement.
var advertisementColumns = AdvertisementColumns{
	Id:           "id",
	PositionCode: "position_code",
	SortOrder:    "sort_order",
	Status:       "status",
	StartTime:    "start_time",
	EndTime:      "end_time",
	AdminId:      "admin_id",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
}

// NewAdvertisementDao creates and returns a new DAO object for table data access.
func NewAdvertisementDao(handlers ...gdb.ModelHandler) *AdvertisementDao {
	return &AdvertisementDao{
		group:    "islamic_content_svc",
		table:    "advertisement",
		columns:  advertisementColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdvertisementDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdvertisementDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdvertisementDao) Columns() AdvertisementColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdvertisementDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdvertisementDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdvertisementDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
