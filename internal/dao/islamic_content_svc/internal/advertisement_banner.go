// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementBannerDao is the data access object for the table advertisement_banner.
type AdvertisementBannerDao struct {
	table    string                     // table is the underlying table name of the DAO.
	group    string                     // group is the database configuration group name of the current DAO.
	columns  AdvertisementBannerColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler         // handlers for customized model modification.
}

// AdvertisementBannerColumns defines and stores column names for the table advertisement_banner.
type AdvertisementBannerColumns struct {
	Id              string // 主键ID
	AdvertisementId string // 广告ID
	LanguageId      string // 语言ID: 0-中文, 1-英文, 2-印尼语
	ImageUrl        string // Banner图片URL
	LinkUrl         string // 跳转链接URL
	SortOrder       string // 图片排序权重，数字越小越靠前
	CreateTime      string // 创建时间(毫秒时间戳)
	UpdateTime      string // 更新时间(毫秒时间戳)
}

// advertisementBannerColumns holds the columns for the table advertisement_banner.
var advertisementBannerColumns = AdvertisementBannerColumns{
	Id:              "id",
	AdvertisementId: "advertisement_id",
	LanguageId:      "language_id",
	ImageUrl:        "image_url",
	LinkUrl:         "link_url",
	SortOrder:       "sort_order",
	CreateTime:      "create_time",
	UpdateTime:      "update_time",
}

// NewAdvertisementBannerDao creates and returns a new DAO object for table data access.
func NewAdvertisementBannerDao(handlers ...gdb.ModelHandler) *AdvertisementBannerDao {
	return &AdvertisementBannerDao{
		group:    "islamic_content_svc",
		table:    "advertisement_banner",
		columns:  advertisementBannerColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdvertisementBannerDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdvertisementBannerDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdvertisementBannerDao) Columns() AdvertisementBannerColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdvertisementBannerDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdvertisementBannerDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdvertisementBannerDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
