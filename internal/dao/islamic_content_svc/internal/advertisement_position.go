// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementPositionDao is the data access object for the table advertisement_position.
type AdvertisementPositionDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  AdvertisementPositionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// AdvertisementPositionColumns defines and stores column names for the table advertisement_position.
type AdvertisementPositionColumns struct {
	Id           string // 主键ID
	PositionName string // 广告位名称，不可修改
	PositionCode string // 位置编码，自动生成，不可修改
	Remark       string // 备注，对该广告位置的备注，可修改
	CreateTime   string // 创建时间(毫秒时间戳)
	UpdateTime   string // 更新时间(毫秒时间戳)
}

// advertisementPositionColumns holds the columns for the table advertisement_position.
var advertisementPositionColumns = AdvertisementPositionColumns{
	Id:           "id",
	PositionName: "position_name",
	PositionCode: "position_code",
	Remark:       "remark",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
}

// NewAdvertisementPositionDao creates and returns a new DAO object for table data access.
func NewAdvertisementPositionDao(handlers ...gdb.ModelHandler) *AdvertisementPositionDao {
	return &AdvertisementPositionDao{
		group:    "islamic_content_svc",
		table:    "advertisement_position",
		columns:  advertisementPositionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdvertisementPositionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdvertisementPositionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdvertisementPositionDao) Columns() AdvertisementPositionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdvertisementPositionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdvertisementPositionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdvertisementPositionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
