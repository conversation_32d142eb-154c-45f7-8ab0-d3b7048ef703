// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DoaReadDao is the data access object for the table doa_read.
type DoaReadDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  DoaReadColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// DoaReadColumns defines and stores column names for the table doa_read.
type DoaReadColumns struct {
	Id         string //
	UserId     string // 用户id
	Types      string // 类型 1-doa,2-wirid- 废弃
	PId        string // 父级id
	BaccanId   string // baccan_id-sub-cate-id
	PName      string // 父级名称
	BaccanName string // 名称
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未更新
}

// doaReadColumns holds the columns for the table doa_read.
var doaReadColumns = DoaReadColumns{
	Id:         "id",
	UserId:     "user_id",
	Types:      "types",
	PId:        "p_id",
	BaccanId:   "baccan_id",
	PName:      "p_name",
	BaccanName: "baccan_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewDoaReadDao creates and returns a new DAO object for table data access.
func NewDoaReadDao(handlers ...gdb.ModelHandler) *DoaReadDao {
	return &DoaReadDao{
		group:    "islamic_content_svc",
		table:    "doa_read",
		columns:  doaReadColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DoaReadDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DoaReadDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DoaReadDao) Columns() DoaReadColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DoaReadDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DoaReadDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DoaReadDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
