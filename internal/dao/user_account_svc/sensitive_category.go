// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// sensitiveCategoryDao is the data access object for the table sensitive_category.
// You can define custom methods on it to extend its functionality as needed.
type sensitiveCategoryDao struct {
	*internal.SensitiveCategoryDao
}

var (
	// SensitiveCategory is a globally accessible object for table sensitive_category operations.
	SensitiveCategory = sensitiveCategoryDao{internal.NewSensitiveCategoryDao()}
)

// Add your custom methods and functionality below.
