// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userMessageDao is the data access object for the table user_message.
// You can define custom methods on it to extend its functionality as needed.
type userMessageDao struct {
	*internal.UserMessageDao
}

var (
	// UserMessage is a globally accessible object for table user_message operations.
	UserMessage = userMessageDao{internal.NewUserMessageDao()}
)

// Add your custom methods and functionality below.
