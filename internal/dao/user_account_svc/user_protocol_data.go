// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userProtocolDataDao is the data access object for the table user_protocol_data.
// You can define custom methods on it to extend its functionality as needed.
type userProtocolDataDao struct {
	*internal.UserProtocolDataDao
}

var (
	// UserProtocolData is a globally accessible object for table user_protocol_data operations.
	UserProtocolData = userProtocolDataDao{internal.NewUserProtocolDataDao()}
)

// Add your custom methods and functionality below.
