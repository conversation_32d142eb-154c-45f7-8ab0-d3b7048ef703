// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userNuAuditDao is the data access object for the table user_nu_audit.
// You can define custom methods on it to extend its functionality as needed.
type userNuAuditDao struct {
	*internal.UserNuAuditDao
}

var (
	// UserNuAudit is a globally accessible object for table user_nu_audit operations.
	UserNuAudit = userNuAuditDao{internal.NewUserNuAuditDao()}
)

// Add your custom methods and functionality below.
