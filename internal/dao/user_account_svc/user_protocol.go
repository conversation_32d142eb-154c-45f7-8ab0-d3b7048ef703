// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userProtocolDao is the data access object for the table user_protocol.
// You can define custom methods on it to extend its functionality as needed.
type userProtocolDao struct {
	*internal.UserProtocolDao
}

var (
	// UserProtocol is a globally accessible object for table user_protocol operations.
	UserProtocol = userProtocolDao{internal.NewUserProtocolDao()}
)

// Add your custom methods and functionality below.
