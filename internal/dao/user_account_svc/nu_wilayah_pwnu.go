// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// nuWilayahPwnuDao is the data access object for the table nu_wilayah_pwnu.
// You can define custom methods on it to extend its functionality as needed.
type nuWilayahPwnuDao struct {
	*internal.NuWilayahPwnuDao
}

var (
	// NuWilayahPwnu is a globally accessible object for table nu_wilayah_pwnu operations.
	NuWilayahPwnu = nuWilayahPwnuDao{internal.NewNuWilayahPwnuDao()}
)

// Add your custom methods and functionality below.
