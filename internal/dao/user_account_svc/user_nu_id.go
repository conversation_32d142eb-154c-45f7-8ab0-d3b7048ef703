// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userNuIdDao is the data access object for the table user_nu_id.
// You can define custom methods on it to extend its functionality as needed.
type userNuIdDao struct {
	*internal.UserNuIdDao
}

var (
	// UserNuId is a globally accessible object for table user_nu_id operations.
	UserNuId = userNuIdDao{internal.NewUserNuIdDao()}
)

// Add your custom methods and functionality below.
