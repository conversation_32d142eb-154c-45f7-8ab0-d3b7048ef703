// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SensitiveDao is the data access object for the table sensitive.
type SensitiveDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SensitiveColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SensitiveColumns defines and stores column names for the table sensitive.
type SensitiveColumns struct {
	Id         string //
	CategoryId string // 分类ID
	Word       string // 敏感词
	MatchType  string // 匹配方式：0：全匹配 1：模糊匹配 2：正则匹配
	Status     string // 是否启用：1开 2关
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

// sensitiveColumns holds the columns for the table sensitive.
var sensitiveColumns = SensitiveColumns{
	Id:         "id",
	CategoryId: "category_id",
	Word:       "word",
	MatchType:  "match_type",
	Status:     "status",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewSensitiveDao creates and returns a new DAO object for table data access.
func NewSensitiveDao(handlers ...gdb.ModelHandler) *SensitiveDao {
	return &SensitiveDao{
		group:    "user_account_svc",
		table:    "sensitive",
		columns:  sensitiveColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SensitiveDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SensitiveDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SensitiveDao) Columns() SensitiveColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SensitiveDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SensitiveDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SensitiveDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
