// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SensitiveCategoryDao is the data access object for the table sensitive_category.
type SensitiveCategoryDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  SensitiveCategoryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// SensitiveCategoryColumns defines and stores column names for the table sensitive_category.
type SensitiveCategoryColumns struct {
	Id         string //
	Name       string // 敏感词
	Sort       string // 排序，0～9999
	CreateTime string // 创建时间
	UpdateTime string // 更新时间
}

// sensitiveCategoryColumns holds the columns for the table sensitive_category.
var sensitiveCategoryColumns = SensitiveCategoryColumns{
	Id:         "id",
	Name:       "name",
	Sort:       "sort",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewSensitiveCategoryDao creates and returns a new DAO object for table data access.
func NewSensitiveCategoryDao(handlers ...gdb.ModelHandler) *SensitiveCategoryDao {
	return &SensitiveCategoryDao{
		group:    "user_account_svc",
		table:    "sensitive_category",
		columns:  sensitiveCategoryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SensitiveCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SensitiveCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SensitiveCategoryDao) Columns() SensitiveCategoryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SensitiveCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SensitiveCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SensitiveCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
