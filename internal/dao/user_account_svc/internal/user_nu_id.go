// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserNuIdDao is the data access object for the table user_nu_id.
type UserNuIdDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserNuIdColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserNuIdColumns defines and stores column names for the table user_nu_id.
type UserNuIdColumns struct {
	Id          string // 主键ID
	UserId      string // 用户ID
	RealName    string // 用户真实姓名
	Gender      string // 性别：0未知 1男 2女
	BirthDate   string // 出生年月日
	NuIdNumber  string // NU会员编号，例如 NU-2024-JAWA-001
	WilayahPwnu string // 所属PWNU地区,nu地址，从nu_wilayah_pwnu选择
	CertFileUrl string // NU认证材料图片URL（证件照/证书）
	Status      string // 认证状态：1待审核 2已通过 3已驳回
	Remark      string // 审核备注，如驳回原因
	ReviewedAt  string // 审核时间
	CreateTime  string // 创建时间
	UpdateTime  string // 更新时间，0代表创建后未被修改过
}

// userNuIdColumns holds the columns for the table user_nu_id.
var userNuIdColumns = UserNuIdColumns{
	Id:          "id",
	UserId:      "user_id",
	RealName:    "real_name",
	Gender:      "gender",
	BirthDate:   "birth_date",
	NuIdNumber:  "nu_id_number",
	WilayahPwnu: "wilayah_pwnu",
	CertFileUrl: "cert_file_url",
	Status:      "status",
	Remark:      "remark",
	ReviewedAt:  "reviewed_at",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewUserNuIdDao creates and returns a new DAO object for table data access.
func NewUserNuIdDao(handlers ...gdb.ModelHandler) *UserNuIdDao {
	return &UserNuIdDao{
		group:    "user_account_svc",
		table:    "user_nu_id",
		columns:  userNuIdColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserNuIdDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserNuIdDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserNuIdDao) Columns() UserNuIdColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserNuIdDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserNuIdDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserNuIdDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
