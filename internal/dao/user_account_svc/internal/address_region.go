// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AddressRegionDao is the data access object for the table address_region.
type AddressRegionDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  AddressRegionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// AddressRegionColumns defines and stores column names for the table address_region.
type AddressRegionColumns struct {
	Id         string //
	Level      string // 1:省, 2:市, 3:区/镇, 4:村/社区
	Code       string // 本级区域代码，唯一标识
	Name       string // 区域名称
	ParentCode string // 上级区域 code，便于递归查询
	Zipcode    string // 邮政编码，仅第4级有用
}

// addressRegionColumns holds the columns for the table address_region.
var addressRegionColumns = AddressRegionColumns{
	Id:         "id",
	Level:      "level",
	Code:       "code",
	Name:       "name",
	ParentCode: "parent_code",
	Zipcode:    "zipcode",
}

// NewAddressRegionDao creates and returns a new DAO object for table data access.
func NewAddressRegionDao(handlers ...gdb.ModelHandler) *AddressRegionDao {
	return &AddressRegionDao{
		group:    "user_account_svc",
		table:    "address_region",
		columns:  addressRegionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AddressRegionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AddressRegionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AddressRegionDao) Columns() AddressRegionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AddressRegionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AddressRegionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AddressRegionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
