// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// addressRegionDao is the data access object for the table address_region.
// You can define custom methods on it to extend its functionality as needed.
type addressRegionDao struct {
	*internal.AddressRegionDao
}

var (
	// AddressRegion is a globally accessible object for table address_region operations.
	AddressRegion = addressRegionDao{internal.NewAddressRegionDao()}
)

// Add your custom methods and functionality below.
