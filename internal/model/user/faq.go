package model

import (
	entity "gtcms/internal/model/entity/islamic_content_svc"
)

type FaqCateWithLanguage struct {
	entity.FaqCate
	Item  []*entity.FaqCateLanguage `orm:"with:faq_cate_id=id" json:"item"` // 一对多
	Title string                    `json:"title"`
}

type FaqQuestionWithLanguage struct {
	entity.FaqQuestion
	Language     []*entity.FaqQuestionLanguage `orm:"with:faq_question_id=id" json:"language"`     // 一对多
	ShowPosition []*entity.FaqShowPosition     `orm:"with:faq_question_id=id" json:"showPosition"` // // 一对多
}

type WisdomCateWithLanguage struct {
	entity.WisdomCate
	Item  []*entity.WisdomCateLanguage `orm:"with:wisdom_cate_id=id" json:"item"` // 一对多
	Title string                       `json:"title"`
}

type WisdomWithLanguage struct {
	entity.Wisdom
	Item         []*entity.WisdomLanguage      `orm:"with:wisdom_id=id" json:"item"`
	Title        string                        `json:"title"`
	Cate         []*entity.WisdomCateLanguage  `orm:"with:wisdom_cate_id=wisdom_cate_id" json:"cate"`
	CateTitle    string                        `json:"cateTitle"`
	ArticleTitle string                        `json:"articleTitle"`
	Article      []*entity.NewsArticleLanguage `json:"article" orm:"with:article_id=article_id"`
}
