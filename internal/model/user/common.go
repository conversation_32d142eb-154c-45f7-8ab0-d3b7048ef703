package model

type ListInput struct {
	Current  int    `v:"required|integer" json:"current" d:"1" dc:"当前页码"`
	PageSize int    `v:"required|integer|between:1,10000" json:"pageSize" d:"15" dc:"每页数量"` // 每页数
	OrderBy  string `json:"orderBy" dc:"参考格式 \"order_by desc,id desc\""`                    // 排序方式
	Offset   int    `v:"integer " json:"Offset" dc:"偏移量 解决深分页"`
}

type ListOutput struct {
	Current int `json:"current" dc:"当前页码"`
	Total   int `json:"total" dc:"总条数"`
	Offset  int `json:"offset" dc:"当前数据最大的偏移量 下次分页传过来"`
}
