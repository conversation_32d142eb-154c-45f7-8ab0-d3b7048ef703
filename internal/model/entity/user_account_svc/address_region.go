// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// AddressRegion is the golang structure for table address_region.
type AddressRegion struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	Level      uint   `json:"level"      orm:"level"       description:"1:省, 2:市, 3:区/镇, 4:村/社区"`
	Code       string `json:"code"       orm:"code"        description:"本级区域代码，唯一标识"`
	Name       string `json:"name"       orm:"name"        description:"区域名称"`
	ParentCode string `json:"parentCode" orm:"parent_code" description:"上级区域 code，便于递归查询"`
	Zipcode    string `json:"zipcode"    orm:"zipcode"     description:"邮政编码，仅第4级有用"`
}
