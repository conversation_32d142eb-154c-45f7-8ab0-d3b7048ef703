// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserMessage is the golang structure for table user_message.
type UserMessage struct {
	Id          uint64 `json:"id"          orm:"id"           description:""`
	UserId      uint64 `json:"userId"      orm:"user_id"      description:"用户ID"`
	MessageType int    `json:"messageType" orm:"message_type" description:"消息分类"`
	ContentType int    `json:"contentType" orm:"content_type" description:"1文字"`
	Content     []byte `json:"content"     orm:"content"      description:"内容"`
	ReadTime    int64  `json:"readTime"    orm:"read_time"    description:"阅读时间"`
	CreatedTime int64  `json:"createdTime" orm:"created_time" description:"时间"`
	DeletedTime int64  `json:"deletedTime" orm:"deleted_time" description:"删除时间"`
}
