// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// SensitiveCategory is the golang structure for table sensitive_category.
type SensitiveCategory struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	Name       string `json:"name"       orm:"name"        description:"敏感词"`
	Sort       int    `json:"sort"       orm:"sort"        description:"排序，0～9999"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间"`
}
