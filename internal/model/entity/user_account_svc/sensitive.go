// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// Sensitive is the golang structure for table sensitive.
type Sensitive struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	CategoryId int    `json:"categoryId" orm:"category_id" description:"分类ID"`
	Word       string `json:"word"       orm:"word"        description:"敏感词"`
	MatchType  int    `json:"matchType"  orm:"match_type"  description:"匹配方式：0：全匹配 1：模糊匹配 2：正则匹配"`
	Status     int    `json:"status"     orm:"status"      description:"是否启用：1开 2关"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间"`
}
