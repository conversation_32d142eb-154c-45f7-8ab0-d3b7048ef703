// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserNuId is the golang structure for table user_nu_id.
type UserNuId struct {
	Id          uint64      `json:"id"          orm:"id"            description:"主键ID"`
	UserId      uint64      `json:"userId"      orm:"user_id"       description:"用户ID"`
	RealName    string      `json:"realName"    orm:"real_name"     description:"用户真实姓名"`
	Gender      string      `json:"gender"      orm:"gender"        description:"性别：0未知 1男 2女"`
	BirthDate   *gtime.Time `json:"birthDate"   orm:"birth_date"    description:"出生年月日"`
	NuIdNumber  string      `json:"nuIdNumber"  orm:"nu_id_number"  description:"NU会员编号，例如 NU-2024-JAWA-001"`
	WilayahPwnu string      `json:"wilayahPwnu" orm:"wilayah_pwnu"  description:"所属PWNU地区,nu地址，从nu_wilayah_pwnu选择"`
	CertFileUrl string      `json:"certFileUrl" orm:"cert_file_url" description:"NU认证材料图片URL（证件照/证书）"`
	Status      int         `json:"status"      orm:"status"        description:"认证状态：1待审核 2已通过 3已驳回"`
	Remark      string      `json:"remark"      orm:"remark"        description:"审核备注，如驳回原因"`
	ReviewedAt  int64       `json:"reviewedAt"  orm:"reviewed_at"   description:"审核时间"`
	CreateTime  int64       `json:"createTime"  orm:"create_time"   description:"创建时间"`
	UpdateTime  int64       `json:"updateTime"  orm:"update_time"   description:"更新时间，0代表创建后未被修改过"`
}
