// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserNuAudit is the golang structure for table user_nu_audit.
type UserNuAudit struct {
	Id               int    `json:"id"               orm:"id"                 description:""`
	UserNuId         int    `json:"userNuId"         orm:"user_nu_id"         description:"nu认证表id"`
	AuditStatus      int    `json:"auditStatus"      orm:"audit_status"       description:"认证状态：1待审核 2已通过 3已驳回"`
	AuditAccount     string `json:"auditAccount"     orm:"audit_account"      description:"审核人账号"`
	AuditAccountName string `json:"auditAccountName" orm:"audit_account_name" description:"审核人名称"`
	AuditReason      string `json:"auditReason"      orm:"audit_reason"       description:"审核原因"`
	CreateTime       int64  `json:"createTime"       orm:"create_time"        description:"创建时间(毫秒时间戳)"`
	UpdateTime       int64  `json:"updateTime"       orm:"update_time"        description:"更新时间(毫秒时间戳)"`
}
