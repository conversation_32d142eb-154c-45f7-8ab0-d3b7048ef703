// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserProtocolData is the golang structure for table user_protocol_data.
type UserProtocolData struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	TypeId     int    `json:"typeId"     orm:"type_id"     description:"协议类型 0：用户协议 1：隐私政策"`
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言id,0-中文，1-英文，2-印尼语"`
	Content    string `json:"content"    orm:"content"     description:"内容"`
}
