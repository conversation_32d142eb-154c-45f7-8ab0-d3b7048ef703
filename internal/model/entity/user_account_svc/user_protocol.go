// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserProtocol is the golang structure for table user_protocol.
type UserProtocol struct {
	Id   uint `json:"id"   orm:"id"   description:""`
	Type int  `json:"type" orm:"type" description:"协议类型 0：用户协议 1：隐私政策"`
}
