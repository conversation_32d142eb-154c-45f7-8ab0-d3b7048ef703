// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package notify_svc

// MessageLanguage is the golang structure for table message_language.
type MessageLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	MessageId  int    `json:"messageId"  orm:"message_id"  description:""`
	LanguageId int    `json:"languageId" orm:"language_id" description:""`
	Title      string `json:"title"      orm:"title"       description:""`
	Content    string `json:"content"    orm:"content"     description:""`
}
