// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package notify_svc

// MessageUser is the golang structure for table message_user.
type MessageUser struct {
	Id         uint  `json:"id"         orm:"id"          description:""`
	MessageId  uint  `json:"messageId"  orm:"message_id"  description:"消息id"`
	UserId     uint  `json:"userId"     orm:"user_id"     description:"收件人id"`
	IsRead     int   `json:"isRead"     orm:"is_read"     description:"是否已读"`
	ReadTime   int64 `json:"readTime"   orm:"read_time"   description:"读件时间"`
	CreateTime int64 `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64 `json:"updateTime" orm:"update_time" description:"更新时间"`
}
