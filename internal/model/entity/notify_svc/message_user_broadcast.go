// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package notify_svc

// MessageUserBroadcast is the golang structure for table message_user_broadcast.
type MessageUserBroadcast struct {
	Id              uint  `json:"id"              orm:"id"                description:""`
	BroadcastType   int   `json:"broadcastType"   orm:"broadcast_type"    description:"广播类型 1 站内信"`
	UserId          uint  `json:"userId"          orm:"user_id"           description:"用户id"`
	LastBroadcastId uint  `json:"lastBroadcastId" orm:"last_broadcast_id" description:"上一次的广播id"`
	CreateTime      int64 `json:"createTime"      orm:"create_time"       description:"创建时间"`
	UpdateTime      int64 `json:"updateTime"      orm:"update_time"       description:"更新时间"`
}
