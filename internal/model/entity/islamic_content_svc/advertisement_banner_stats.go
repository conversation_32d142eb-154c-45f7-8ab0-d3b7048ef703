// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// AdvertisementBannerStats is the golang structure for table advertisement_banner_stats.
type AdvertisementBannerStats struct {
	Id              uint64 `json:"id"              orm:"id"               description:"主键ID"`
	BannerId        uint   `json:"bannerId"        orm:"banner_id"        description:"广告ID"`
	AdvertisementId uint   `json:"advertisementId" orm:"advertisement_id" description:"广告ID"`
	LanguageId      uint   `json:"languageId"      orm:"language_id"      description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	UserId          uint64 `json:"userId"          orm:"user_id"          description:"用户ID，0表示未登录用户"`
	DeviceId        string `json:"deviceId"        orm:"device_id"        description:"设备唯一标识"`
	IpAddress       string `json:"ipAddress"       orm:"ip_address"       description:"IP地址"`
	UserAgent       string `json:"userAgent"       orm:"user_agent"       description:"用户代理信息"`
	CreateTime      uint64 `json:"createTime"      orm:"create_time"      description:"操作时间(毫秒时间戳)"`
}
