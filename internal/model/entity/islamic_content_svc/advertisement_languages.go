// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// AdvertisementLanguages is the golang structure for table advertisement_languages.
type AdvertisementLanguages struct {
	Id               uint   `json:"id"               orm:"id"                description:"主键ID"`
	AdvertisementId  uint   `json:"advertisementId"  orm:"advertisement_id"  description:"广告ID"`
	LanguageId       uint   `json:"languageId"       orm:"language_id"       description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	Title            string `json:"title"            orm:"title"             description:"广告名称"`
	Description      string `json:"description"      orm:"description"       description:"广告描述"`
	DisplayType      uint   `json:"displayType"      orm:"display_type"      description:"显示类型: 1-单图固定, 2-多图轮播"`
	CarouselInterval uint   `json:"carouselInterval" orm:"carousel_interval" description:"轮播间隔时间(秒)，仅多图轮播时有效"`
	AdminId          uint   `json:"adminId"          orm:"admin_id"          description:"创建管理员ID"`
	CreateTime       uint64 `json:"createTime"       orm:"create_time"       description:"创建时间(毫秒时间戳)"`
	UpdateTime       uint64 `json:"updateTime"       orm:"update_time"       description:"更新时间(毫秒时间戳)"`
}
