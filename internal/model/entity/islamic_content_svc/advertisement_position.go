// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// AdvertisementPosition is the golang structure for table advertisement_position.
type AdvertisementPosition struct {
	Id           uint   `json:"id"           orm:"id"            description:"主键ID"`
	PositionName string `json:"positionName" orm:"position_name" description:"广告位名称，不可修改"`
	PositionCode string `json:"positionCode" orm:"position_code" description:"位置编码，自动生成，不可修改"`
	Remark       string `json:"remark"       orm:"remark"        description:"备注，对该广告位置的备注，可修改"`
	CreateTime   uint64 `json:"createTime"   orm:"create_time"   description:"创建时间(毫秒时间戳)"`
	UpdateTime   uint64 `json:"updateTime"   orm:"update_time"   description:"更新时间(毫秒时间戳)"`
}
