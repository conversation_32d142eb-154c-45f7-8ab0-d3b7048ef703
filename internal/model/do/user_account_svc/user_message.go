// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserMessage is the golang structure of table user_message for DAO operations like Where/Data.
type UserMessage struct {
	g.Meta      `orm:"table:user_message, do:true"`
	Id          interface{} //
	UserId      interface{} // 用户ID
	MessageType interface{} // 消息分类
	ContentType interface{} // 1文字
	Content     []byte      // 内容
	ReadTime    interface{} // 阅读时间
	CreatedTime interface{} // 时间
	DeletedTime interface{} // 删除时间
}
