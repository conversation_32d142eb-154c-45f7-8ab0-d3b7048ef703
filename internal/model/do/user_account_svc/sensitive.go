// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Sensitive is the golang structure of table sensitive for DAO operations like Where/Data.
type Sensitive struct {
	g.Meta     `orm:"table:sensitive, do:true"`
	Id         interface{} //
	CategoryId interface{} // 分类ID
	Word       interface{} // 敏感词
	MatchType  interface{} // 匹配方式：0：全匹配 1：模糊匹配 2：正则匹配
	Status     interface{} // 是否启用：1开 2关
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 更新时间
}
