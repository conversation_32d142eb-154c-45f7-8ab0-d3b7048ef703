// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SensitiveCategory is the golang structure of table sensitive_category for DAO operations like Where/Data.
type SensitiveCategory struct {
	g.Meta     `orm:"table:sensitive_category, do:true"`
	Id         interface{} //
	Name       interface{} // 敏感词
	Sort       interface{} // 排序，0～9999
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 更新时间
}
