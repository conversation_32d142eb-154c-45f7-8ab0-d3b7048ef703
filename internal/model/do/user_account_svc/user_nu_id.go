// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserNuId is the golang structure of table user_nu_id for DAO operations like Where/Data.
type UserNuId struct {
	g.Meta      `orm:"table:user_nu_id, do:true"`
	Id          interface{} // 主键ID
	UserId      interface{} // 用户ID
	RealName    interface{} // 用户真实姓名
	Gender      interface{} // 性别：0未知 1男 2女
	BirthDate   *gtime.Time // 出生年月日
	NuIdNumber  interface{} // NU会员编号，例如 NU-2024-JAWA-001
	WilayahPwnu interface{} // 所属PWNU地区,nu地址，从nu_wilayah_pwnu选择
	CertFileUrl interface{} // NU认证材料图片URL（证件照/证书）
	Status      interface{} // 认证状态：1待审核 2已通过 3已驳回
	Remark      interface{} // 审核备注，如驳回原因
	ReviewedAt  interface{} // 审核时间
	CreateTime  interface{} // 创建时间
	UpdateTime  interface{} // 更新时间，0代表创建后未被修改过
}
