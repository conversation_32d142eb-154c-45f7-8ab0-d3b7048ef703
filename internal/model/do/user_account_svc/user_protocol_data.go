// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserProtocolData is the golang structure of table user_protocol_data for DAO operations like Where/Data.
type UserProtocolData struct {
	g.Meta     `orm:"table:user_protocol_data, do:true"`
	Id         interface{} //
	TypeId     interface{} // 协议类型 0：用户协议 1：隐私政策
	LanguageId interface{} // 语言id,0-中文，1-英文，2-印尼语
	Content    interface{} // 内容
}
