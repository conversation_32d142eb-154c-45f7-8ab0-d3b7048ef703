// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserProtocol is the golang structure of table user_protocol for DAO operations like Where/Data.
type UserProtocol struct {
	g.Meta `orm:"table:user_protocol, do:true"`
	Id     interface{} //
	Type   interface{} // 协议类型 0：用户协议 1：隐私政策
}
