// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserNuAudit is the golang structure of table user_nu_audit for DAO operations like Where/Data.
type UserNuAudit struct {
	g.Meta           `orm:"table:user_nu_audit, do:true"`
	Id               interface{} //
	UserNuId         interface{} // nu认证表id
	AuditStatus      interface{} // 认证状态：1待审核 2已通过 3已驳回
	AuditAccount     interface{} // 审核人账号
	AuditAccountName interface{} // 审核人名称
	AuditReason      interface{} // 审核原因
	CreateTime       interface{} // 创建时间(毫秒时间戳)
	UpdateTime       interface{} // 更新时间(毫秒时间戳)
}
