// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NuWilayahPwnu is the golang structure of table nu_wilayah_pwnu for DAO operations like Where/Data.
type NuWilayahPwnu struct {
	g.Meta `orm:"table:nu_wilayah_pwnu, do:true"`
	Id     interface{} // 唯一标识，自增主键
	Name   interface{} //
}
