// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AdvertisementLanguages is the golang structure of table advertisement_languages for DAO operations like Where/Data.
type AdvertisementLanguages struct {
	g.Meta           `orm:"table:advertisement_languages, do:true"`
	Id               interface{} // 主键ID
	AdvertisementId  interface{} // 广告ID
	LanguageId       interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title            interface{} // 广告名称
	Description      interface{} // 广告描述
	DisplayType      interface{} // 显示类型: 1-单图固定, 2-多图轮播
	CarouselInterval interface{} // 轮播间隔时间(秒)，仅多图轮播时有效
	AdminId          interface{} // 创建管理员ID
	CreateTime       interface{} // 创建时间(毫秒时间戳)
	UpdateTime       interface{} // 更新时间(毫秒时间戳)
}
