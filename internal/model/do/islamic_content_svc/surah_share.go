// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SurahShare is the golang structure of table surah_share for DAO operations like Where/Data.
type SurahShare struct {
	g.Meta     `orm:"table:surah_share, do:true"`
	Id         interface{} //
	UserId     interface{} // 用户id
	AyahId     interface{} // ayah_id节id
	SurahName  interface{} // 名称
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未更新
}
