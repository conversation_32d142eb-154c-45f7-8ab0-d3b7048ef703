// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package notify_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// MessageLanguage is the golang structure of table message_language for DAO operations like Where/Data.
type MessageLanguage struct {
	g.Meta     `orm:"table:message_language, do:true"`
	Id         interface{} //
	MessageId  interface{} //
	LanguageId interface{} //
	Title      interface{} //
	Content    interface{} //
}
