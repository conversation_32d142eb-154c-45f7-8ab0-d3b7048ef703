// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package notify_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// MessageUserBroadcast is the golang structure of table message_user_broadcast for DAO operations like Where/Data.
type MessageUserBroadcast struct {
	g.Meta          `orm:"table:message_user_broadcast, do:true"`
	Id              interface{} //
	BroadcastType   interface{} // 广播类型 1 站内信
	UserId          interface{} // 用户id
	LastBroadcastId interface{} // 上一次的广播id
	CreateTime      interface{} // 创建时间
	UpdateTime      interface{} // 更新时间
}
