package nu

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/golang/glog"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) UserNuList(ctx context.Context, req *v1.UserNuListReq) (res *v1.UserNuListRes, err error) {
	res, err = service.UserNu().UserNuList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) UserNuAudit(ctx context.Context, req *v1.UserNuAuditReq) (res *v1.UserNuAuditRes, err error) {
	res, err = service.UserNu().UserNuAudit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) UserNuOne(ctx context.Context, req *v1.UserNuOneReq) (res *v1.UserNuOneRes, err error) {
	res, err = service.UserNu().UserNuOne(ctx, req)
	if err != nil {
		glog.Error(ctx, err)
	}
	return
}
