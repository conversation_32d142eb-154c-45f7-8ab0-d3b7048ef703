package hajiLandmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// ==================== 地标类型相关接口 ====================

func (c *Controller) TypeList(ctx context.Context, req *v1.HajiLandmarkTypeListReq) (res *v1.HajiLandmarkTypeListRes, err error) {
	res, err = service.HajiLandmark().TypeList(ctx, req)
	return
}

func (c *Controller) TypeAdd(ctx context.Context, req *v1.HajiLandmarkTypeCreateReq) (res *v1.HajiLandmarkTypeCreateRes, err error) {
	res, err = service.HajiLandmark().TypeAdd(ctx, req)
	return
}

func (c *Controller) TypeEdit(ctx context.Context, req *v1.HajiLandmarkTypeEditReq) (res *v1.HajiLandmarkTypeEditRes, err error) {
	res, err = service.HajiLandmark().TypeEdit(ctx, req)
	return
}

func (c *Controller) TypeOne(ctx context.Context, req *v1.HajiLandmarkTypeOneReq) (res *v1.HajiLandmarkTypeOneRes, err error) {
	res, err = service.HajiLandmark().TypeOne(ctx, req)
	return
}

func (c *Controller) TypeDelete(ctx context.Context, req *v1.HajiLandmarkTypeDeleteReq) (res *v1.HajiLandmarkTypeDeleteRes, err error) {
	res, err = service.HajiLandmark().TypeDelete(ctx, req)
	return
}

// ==================== 地标相关接口 ====================

func (c *Controller) List(ctx context.Context, req *v1.HajiLandmarkListReq) (res *v1.HajiLandmarkListRes, err error) {
	res, err = service.HajiLandmark().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.HajiLandmarkCreateReq) (res *v1.HajiLandmarkCreateRes, err error) {
	res, err = service.HajiLandmark().Add(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.HajiLandmarkEditReq) (res *v1.HajiLandmarkEditRes, err error) {
	res, err = service.HajiLandmark().Edit(ctx, req)
	return
}

func (c *Controller) One(ctx context.Context, req *v1.HajiLandmarkOneReq) (res *v1.HajiLandmarkOneRes, err error) {
	res, err = service.HajiLandmark().One(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.HajiLandmarkDeleteReq) (res *v1.HajiLandmarkDeleteRes, err error) {
	res, err = service.HajiLandmark().Delete(ctx, req)
	return
}
