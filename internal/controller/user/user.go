package user

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) List(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error) {
	res, err = service.User().List(ctx, req)
	return
}

func (c *Controller) Info(ctx context.Context, req *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {
	res, err = service.User().Info(ctx, req)
	return
}

func (c *Controller) BatchBan(ctx context.Context, req *v1.BatchBanReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.User().BatchBan(ctx, req)
	return
}
func (c *Controller) UserLoginLog(ctx context.Context, req *v1.UserLoginLogReq) (res *v1.UserLoginLogRes, err error) {
	res, err = service.User().UserLoginLog(ctx, req)
	return
}
func (c *Controller) CollectList(ctx context.Context, req *v1.CollectListReq) (res *v1.RecordListRes, err error) {
	res, err = service.User().CollectList(ctx, req)
	return
}
func (c *Controller) ReadList(ctx context.Context, req *v1.ReadListReq) (res *v1.RecordListRes, err error) {
	res, err = service.User().ReadList(ctx, req)
	return
}

func (c *Controller) ShareList(ctx context.Context, req *v1.ShareListReq) (res *v1.RecordListRes, err error) {
	res, err = service.User().ShareList(ctx, req)
	return
}
