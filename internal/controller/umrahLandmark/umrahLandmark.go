package umrahLandmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// ==================== 地标类型相关接口 ====================

func (c *Controller) TypeList(ctx context.Context, req *v1.UmrahLandmarkTypeListReq) (res *v1.UmrahLandmarkTypeListRes, err error) {
	res, err = service.UmrahLandmark().TypeList(ctx, req)
	return
}

func (c *Controller) TypeAdd(ctx context.Context, req *v1.UmrahLandmarkTypeCreateReq) (res *v1.UmrahLandmarkTypeCreateRes, err error) {
	res, err = service.UmrahLandmark().TypeAdd(ctx, req)
	return
}

func (c *Controller) TypeEdit(ctx context.Context, req *v1.UmrahLandmarkTypeEditReq) (res *v1.UmrahLandmarkTypeEditRes, err error) {
	res, err = service.UmrahLandmark().TypeEdit(ctx, req)
	return
}

func (c *Controller) TypeOne(ctx context.Context, req *v1.UmrahLandmarkTypeOneReq) (res *v1.UmrahLandmarkTypeOneRes, err error) {
	res, err = service.UmrahLandmark().TypeOne(ctx, req)
	return
}

func (c *Controller) TypeDelete(ctx context.Context, req *v1.UmrahLandmarkTypeDeleteReq) (res *v1.UmrahLandmarkTypeDeleteRes, err error) {
	res, err = service.UmrahLandmark().TypeDelete(ctx, req)
	return
}

// ==================== 地标相关接口 ====================

func (c *Controller) List(ctx context.Context, req *v1.UmrahLandmarkListReq) (res *v1.UmrahLandmarkListRes, err error) {
	res, err = service.UmrahLandmark().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.UmrahLandmarkCreateReq) (res *v1.UmrahLandmarkCreateRes, err error) {
	res, err = service.UmrahLandmark().Add(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.UmrahLandmarkEditReq) (res *v1.UmrahLandmarkEditRes, err error) {
	res, err = service.UmrahLandmark().Edit(ctx, req)
	return
}

func (c *Controller) One(ctx context.Context, req *v1.UmrahLandmarkOneReq) (res *v1.UmrahLandmarkOneRes, err error) {
	res, err = service.UmrahLandmark().One(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.UmrahLandmarkDeleteReq) (res *v1.UmrahLandmarkDeleteRes, err error) {
	res, err = service.UmrahLandmark().Delete(ctx, req)
	return
}
