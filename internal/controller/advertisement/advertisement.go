package advertisement

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// PositionList 广告位置列表
func (c *Controller) PositionList(ctx context.Context, req *v1.AdvertisementPositionListReq) (out *v1.AdvertisementPositionListRes, err error) {
	out, err = service.Advertisement().PositionList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// PositionEdit 修改广告位置备注
func (c *Controller) PositionEdit(ctx context.Context, req *v1.AdvertisementPositionEditReq) (out *v1.AdvertisementPositionEditRes, err error) {
	out, err = service.Advertisement().PositionEdit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// =====================================================
// 广告管理
// =====================================================

// List 广告列表
func (c *Controller) List(ctx context.Context, req *v1.AdvertisementListReq) (out *v1.AdvertisementListRes, err error) {
	out, err = service.Advertisement().List(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// One 广告详情
func (c *Controller) One(ctx context.Context, req *v1.AdvertisementOneReq) (out *v1.AdvertisementOneRes, err error) {
	out, err = service.Advertisement().One(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Add 新增广告
func (c *Controller) Add(ctx context.Context, req *v1.AdvertisementAddReq) (out *v1.AdvertisementAddRes, err error) {
	out, err = service.Advertisement().Add(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Edit 编辑广告
func (c *Controller) Edit(ctx context.Context, req *v1.AdvertisementEditReq) (out *v1.AdvertisementEditRes, err error) {
	out, err = service.Advertisement().Edit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// Delete 删除广告
func (c *Controller) Delete(ctx context.Context, req *v1.AdvertisementDeleteReq) (out *v1.AdvertisementDeleteRes, err error) {
	out, err = service.Advertisement().Delete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// ToggleStatus 切换广告状态
func (c *Controller) ToggleStatus(ctx context.Context, req *v1.AdvertisementToggleStatusReq) (out *v1.AdvertisementToggleStatusRes, err error) {
	out, err = service.Advertisement().ToggleStatus(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// StatsOverview 广告统计概览
func (c *Controller) StatsOverview(ctx context.Context, req *v1.AdvertisementStatsOverviewReq) (out *v1.AdvertisementStatsOverviewRes, err error) {
	out, err = service.Advertisement().StatsOverview(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

// StatsTrend 广告统计趋势图
func (c *Controller) StatsTrend(ctx context.Context, req *v1.AdvertisementStatsTrendReq) (out *v1.AdvertisementStatsTrendRes, err error) {
	out, err = service.Advertisement().StatsTrend(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
