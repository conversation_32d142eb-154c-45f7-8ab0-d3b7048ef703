package hajiNewsTag

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// 新增朝觐新闻标签
func (c *Controller) Add(ctx context.Context, req *v1.HajiNewsTagAddReq) (out *v1.HajiNewsTagAddRes, err error) {
	out = new(v1.HajiNewsTagAddRes)
	err = service.HajiNewsTag().Add(ctx, req)
	return
}

// 编辑朝觐新闻标签
func (c *Controller) Edit(ctx context.Context, req *v1.HajiNewsTagEditReq) (out *v1.HajiNewsTagEditRes, err error) {
	out = new(v1.HajiNewsTagEditRes)
	err = service.HajiNewsTag().Edit(ctx, req)
	return
}

// 获取朝觐新闻标签详情
func (c *Controller) Info(ctx context.Context, req *v1.HajiNewsTagInfoReq) (out *v1.HajiNewsTagInfoRes, err error) {
	out, err = service.HajiNewsTag().Info(ctx, req)
	return
}

// 获取朝觐新闻标签列表
func (c *Controller) List(ctx context.Context, req *v1.HajiNewsTagListReq) (out *v1.HajiNewsTagListRes, err error) {
	out, err = service.HajiNewsTag().List(ctx, req)
	return
}

// 删除朝觐新闻标签
func (c *Controller) Delete(ctx context.Context, req *v1.HajiNewsTagDeleteReq) (out *v1.HajiNewsTagDeleteRes, err error) {
	out = new(v1.HajiNewsTagDeleteRes)
	err = service.HajiNewsTag().Delete(ctx, req)
	return
}
