package wisdom

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func SetDefaultPage(listReq *v1.ListReq) *v1.ListReq {
	if listReq == nil {
		return &v1.ListReq{
			Current:  1,
			PageSize: 10,
		}
	}
	if listReq.Current == 0 {
		listReq.Current = 1
	}
	if listReq.PageSize == 0 {
		listReq.PageSize = 10
	}
	return listReq
}
func (c *Controller) WisdomCateAdd(ctx context.Context, req *v1.WisdomCateCreateReq) (res *v1.WisdomCateCreateRes, err error) {
	res, err = service.Wisdom().WisdomCateAdd(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomCateEdit(ctx context.Context, req *v1.WisdomCateEditReq) (res *v1.WisdomCateEditRes, err error) {
	res, err = service.Wisdom().WisdomCateEdit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomCateOne(ctx context.Context, req *v1.WisdomCateOneReq) (res *v1.WisdomCateOneRes, err error) {
	res, err = service.Wisdom().WisdomCateOne(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomCateDelete(ctx context.Context, req *v1.WisdomCateDeleteReq) (res *v1.WisdomCateDeleteRes, err error) {
	res, err = service.Wisdom().WisdomCateDelete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomCateList(ctx context.Context, req *v1.WisdomCateListReq) (res *v1.WisdomCateListRes, err error) {
	res, err = service.Wisdom().WisdomCateList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomAdd(ctx context.Context, req *v1.WisdomCreateReq) (res *v1.WisdomCreateRes, err error) {
	res, err = service.Wisdom().WisdomAdd(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomEdit(ctx context.Context, req *v1.WisdomEditReq) (res *v1.WisdomEditRes, err error) {
	res, err = service.Wisdom().WisdomEdit(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
func (c *Controller) WisdomDelete(ctx context.Context, req *v1.WisdomDeleteReq) (res *v1.WisdomDeleteRes, err error) {
	res, err = service.Wisdom().WisdomDelete(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
func (c *Controller) WisdomOne(ctx context.Context, req *v1.WisdomOneReq) (res *v1.WisdomOneRes, err error) {
	res, err = service.Wisdom().WisdomOne(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
func (c *Controller) WisdomList(ctx context.Context, req *v1.WisdomListReq) (res *v1.WisdomListRes, err error) {
	res, err = service.Wisdom().WisdomList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) WisdomCateAll(ctx context.Context, req *v1.WisdomCateAllReq) (res *v1.WisdomCateAllRes, err error) {
	res, err = service.Wisdom().WisdomCateAll(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}

func (c *Controller) Publish(ctx context.Context, req *v1.WisdomPublishReq) (res *v1.WisdomPublishRes, err error) {
	res, err = service.Wisdom().Publish(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return
}
