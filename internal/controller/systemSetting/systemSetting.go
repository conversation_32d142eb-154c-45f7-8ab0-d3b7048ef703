package systemSetting

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 用户协议
func (c *Controller) UserProtocolSet(ctx context.Context, req *v1.UserProtocolSetReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.SystemSetting().UserProtocolSet(ctx, req)
	return
}

// 用户协议详情
func (c *Controller) UserProtocolInfo(ctx context.Context, req *v1.UserProtocolInfoReq) (res *v1.UserProtocolInfoRes, err error) {
	res, err = service.SystemSetting().UserProtocolInfo(ctx, req)
	return
}

// 隐私政策
func (c *Controller) PrivacyPolicySet(ctx context.Context, req *v1.PrivacyPolicySetReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.SystemSetting().PrivacyPolicySet(ctx, req)
	return
}

// 隐私政策详情
func (c *Controller) PrivacyPolicyInfo(ctx context.Context, req *v1.PrivacyPolicyInfoReq) (res *v1.PrivacyPolicyInfoRes, err error) {
	res, err = service.SystemSetting().PrivacyPolicyInfo(ctx, req)
	return
}

// 安全设置(短信及WhatsAPP验证码机制)
func (c *Controller) SafetySettingSet(ctx context.Context, req *v1.SafetySettingSetReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.SystemSetting().SafetySettingSet(ctx, req)
	return
}

// 安全设置详情
func (c *Controller) SafetySettingInfo(ctx context.Context, req *v1.SafetySettingInfoReq) (res *v1.SafetySettingInfoRes, err error) {
	res = new(v1.SafetySettingInfoRes)
	res, err = service.SystemSetting().SafetySettingInfo(ctx, req)
	return
}

// 敏感词过滤
func (c *Controller) SensitiveWordFilteringSet(ctx context.Context, req *v1.SensitiveWordFilteringSetReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.SystemSetting().SensitiveWordFilteringSet(ctx, req)
	return
}

// 敏感词过滤详情
func (c *Controller) SensitiveWordFilteringInfo(ctx context.Context, req *v1.SensitiveWordFilteringInfoReq) (res *v1.SensitiveWordFilteringInfoRes, err error) {
	res = new(v1.SensitiveWordFilteringInfoRes)
	res, err = service.SystemSetting().SensitiveWordFilteringInfo(ctx, req)
	return
}

// 敏感词类型列表
func (c *Controller) SensitiveWordCategoryList(ctx context.Context, req *v1.SensitiveWordCategoryListReq) (res *v1.SensitiveWordCategoryListRes, err error) {
	res = new(v1.SensitiveWordCategoryListRes)
	res, err = service.SensitiveWord().CategoryList(ctx, req)
	return
}

// 敏感词类型详情
func (c *Controller) SensitiveWordCategoryInfo(ctx context.Context, req *v1.SensitiveWordCategoryInfoReq) (res *v1.SensitiveWordCategoryInfoRes, err error) {
	res = new(v1.SensitiveWordCategoryInfoRes)
	res, err = service.SensitiveWord().CategoryInfo(ctx, req)
	return
}

// 敏感词类型添加
func (c *Controller) SensitiveWordCategoryAdd(ctx context.Context, req *v1.SensitiveWordCategoryAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().CategoryAdd(ctx, req)
	return
}

// 敏感词类型编辑
func (c *Controller) SensitiveWordCategoryEdit(ctx context.Context, req *v1.SensitiveWordCategoryEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().CategoryEdit(ctx, req)
	return
}

// 敏感词类型删除
func (c *Controller) SensitiveWordCategoryDel(ctx context.Context, req *v1.SensitiveWordCategoryDelReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().CategoryDel(ctx, req)
	return
}

// 敏感词列表
func (c *Controller) SensitiveWordList(ctx context.Context, req *v1.SensitiveWordListReq) (res *v1.SensitiveWordListRes, err error) {
	res = new(v1.SensitiveWordListRes)
	res, err = service.SensitiveWord().List(ctx, req)
	return
}

// 敏感词类型详情
func (c *Controller) SensitiveWordInfo(ctx context.Context, req *v1.SensitiveWordInfoReq) (res *v1.SensitiveWordInfoRes, err error) {
	res = new(v1.SensitiveWordInfoRes)
	res, err = service.SensitiveWord().Info(ctx, req)
	return
}

// 敏感词类型添加
func (c *Controller) SensitiveWordAdd(ctx context.Context, req *v1.SensitiveWordAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().Add(ctx, req)
	return
}

// 敏感词类型编辑
func (c *Controller) SensitiveWordEdit(ctx context.Context, req *v1.SensitiveWordEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().Edit(ctx, req)
	return
}

// 敏感词类型删除
func (c *Controller) SensitiveWordDel(ctx context.Context, req *v1.SensitiveWordDelReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.SensitiveWord().Delete(ctx, req)
	return
}
