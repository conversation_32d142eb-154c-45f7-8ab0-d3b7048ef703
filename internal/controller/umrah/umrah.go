package umrah

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 获取 Urutan Manasik 列表
func (c *Controller) UrutanManasikList(ctx context.Context, req *v1.UrutanManasikUmrahListReq) (res *v1.UrutanManasikUmrahListRes, err error) {
	res = new(v1.UrutanManasikUmrahListRes)
	res, err = service.Umrah().UrutanManasikUmrahList(ctx, req)
	return
}

// 获取 Urutan Manasik 详情
func (c *Controller) UrutanManasikInfo(ctx context.Context, req *v1.UrutanManasikUmrahInfoReq) (res *v1.UrutanManasikUmrahInfoRes, err error) {
	res = new(v1.UrutanManasikUmrahInfoRes)
	res, err = service.Umrah().UrutanManasikUmrahInfo(ctx, req)
	return
}

// 新增 Urutan Manasik
func (c *Controller) UrutanManasikAdd(ctx context.Context, req *v1.UrutanManasikUmrahAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Umrah().UrutanManasikUmrahAdd(ctx, req)
	return
}

// 编辑 Urutan Manasik
func (c *Controller) UrutanManasikEdit(ctx context.Context, req *v1.UrutanManasikUmrahEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Umrah().UrutanManasikUmrahEdit(ctx, req)
	return
}

// Ringkas 列表
func (c *Controller) DoaRingkasUmrahList(ctx context.Context, req *v1.DoaRingkasUmrahListReq) (res *v1.DoaRingkasUmrahListRes, err error) {
	res = new(v1.DoaRingkasUmrahListRes)
	res, err = service.Umrah().DoaRingkasUmrahList(ctx, req)
	return
}

// Ringkas 详情
func (c *Controller) DoaRingkasUmrahInfo(ctx context.Context, req *v1.DoaRingkasUmrahInfoReq) (res *v1.DoaRingkasUmrahInfoRes, err error) {
	res = new(v1.DoaRingkasUmrahInfoRes)
	res, err = service.Umrah().DoaRingkasUmrahInfo(ctx, req)
	return
}

// Panjang 列表
func (c *Controller) DoaPanjangUmrahList(ctx context.Context, req *v1.DoaPanjangUmrahListReq) (res *v1.DoaPanjangUmrahListRes, err error) {
	res = new(v1.DoaPanjangUmrahListRes)
	res, err = service.Umrah().DoaPanjangUmrahList(ctx, req)
	return
}

// Panjang Bacaan列表
func (c *Controller) DoaPanjangUmrahBacaanList(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListReq) (res *v1.DoaPanjangUmrahBacaanListRes, err error) {
	res = new(v1.DoaPanjangUmrahBacaanListRes)
	res, err = service.Umrah().DoaPanjangUmrahBacaanList(ctx, req)
	return
}

// Panjang Bacaan列表详情
func (c *Controller) DoaPanjangUmrahBacaanListInfo(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListInfoReq) (res *v1.DoaPanjangUmrahBacaanListInfoRes, err error) {
	res = new(v1.DoaPanjangUmrahBacaanListInfoRes)
	res, err = service.Umrah().DoaPanjangUmrahBacaanListInfo(ctx, req)
	return
}

// Hikmah Umrah列表
func (c *Controller) HikmahUmrahList(ctx context.Context, req *v1.HikmahUmrahListReq) (res *v1.HikmahUmrahListRes, err error) {
	res = new(v1.HikmahUmrahListRes)
	res, err = service.Umrah().HikmahUmrahList(ctx, req)
	return
}

// Hikmah Umrah详情
func (c *Controller) HikmahUmrahInfo(ctx context.Context, req *v1.HikmahUmrahInfoReq) (res *v1.HikmahUmrahInfoRes, err error) {
	res = new(v1.HikmahUmrahInfoRes)
	res, err = service.Umrah().HikmahUmrahInfo(ctx, req)
	return
}

// Hikmah Umrah新增
func (c *Controller) HikmahUmrahAdd(ctx context.Context, req *v1.HikmahUmrahAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Umrah().HikmahUmrahAdd(ctx, req)
	return
}

// Hikmah Umrah编辑
func (c *Controller) HikmahUmrahEdit(ctx context.Context, req *v1.HikmahUmrahEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Umrah().HikmahUmrahEdit(ctx, req)
	return
}

// Hikmah Umrah删除
func (c *Controller) HikmahUmrahDelete(ctx context.Context, req *v1.HikmahUmrahDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Umrah().HikmahUmrahDelete(ctx, req)
	return
}
