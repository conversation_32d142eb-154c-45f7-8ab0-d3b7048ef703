server:
  id: "1"
  address: "8001"
  openapiPath: ""
  swaggerPath: ""
  dbCacheCost: 512
  dbCacheNumCounters: 100000

logger:
  path: "/data/logs/gtcms" # 日志文件路径。默认为空，表示关闭，仅输出到终端
  file: "{Y-m-d}.log" # 日志文件格式。默认为"{Y-m-d}.log"
  level: "all" # 日志输出级别
  writerColorEnable: false
  stdout: true # 日志是否同时输出到终端。默认true

database:
  default:
    link: "mysql:root:Ha123456@tcp(***************:3306)/admin"
    debug: true

  admin:
    link: "mysql:root:Ha123456@tcp(***************:3306)/admin"
    debug: true

  islamic_content_svc:
    link: "mysql:root:Ha123456@tcp(***************:3306)/islamic_content_svc"
    debug: true

  user_account_svc:
    link: "mysql:root:Ha123456@tcp(***************:3306)/user_account_svc"
    debug: true

redis:
  default:
    address: "127.0.0.1:6379"
    db: 0

upload:
  path: "/tmp/www/upload"

ipdb:
  ipv4: ./resource/ipdb/IP2Location.ipdb